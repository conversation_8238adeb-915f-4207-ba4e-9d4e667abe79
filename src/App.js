import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from './context/AuthContext'
import { CarProvider } from './context/CarContext'
import { ThemeProvider } from './context/ThemeContext'
import { InterestProvider } from './context/InterestContext'
import Login from './components/Login'
import Dashboard from './components/Dashboard'
import Promotion from './components/promotion/Promotion'
import ProtectedRoute from './components/ProtectedRoute'
import MainLayout from './components/layout/MainLayout'
import './App.css'
import Interest from './components/interest/interest'
import EntryRouter from './components/EntryManager/pages/EntryRouter'

function App() {
  return (
    <ThemeProvider>
      <AuthProvider>
        <CarProvider>
          <InterestProvider>
          <Router>
            <div className="App">
              <Routes>
                <Route path="/login" element={<Login />} />
                <Route element={<MainLayout />}>
                  <Route 
                    path="/dashboard" 
                    element={
                      <ProtectedRoute>
                        <Dashboard />
                      </ProtectedRoute>
                    } 
                  />
                  <Route 
                    path="/promotion" 
                    element={
                      <ProtectedRoute>
                        <Promotion />
                      </ProtectedRoute>
                    } 
                  />
                  <Route 
                    path="/interest" 
                    element={
                      <ProtectedRoute>
                        <Interest />
                      </ProtectedRoute>
                    } 
                  />
                  <Route 
                    path="/entry/:type" 
                    element={
                      <ProtectedRoute>
                        <EntryRouter />
                      </ProtectedRoute>
                    } 
                  />
                </Route>
                <Route path="/" element={<Navigate to="/dashboard" replace />} />
              </Routes>
            </div>
          </Router>
          </InterestProvider>
        </CarProvider>
      </AuthProvider>
    </ThemeProvider>
  )
}

export default App