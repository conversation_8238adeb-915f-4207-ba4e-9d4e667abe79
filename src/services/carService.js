import axios from 'axios'

const API_URL = 'https://n8n-deploy.agilesoftgroup.com/webhook/AICP'

// สร้าง axios instance
const api = axios.create({
  baseURL: API_URL,
  timeout: 15000,
})

// Interceptor สำหรับแนบ token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Interceptor สำหรับจัดการ response
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.data) // Debug log
    return response
  },
  (error) => {
    console.error('API Error:', error)
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Function to transform API data to our format
const transformCarData = (apiCar) => {
  return {
    id: apiCar.carID,
    title: apiCar.carTitleEN || apiCar.carTitleKM || 'No Title',
    brand: apiCar.carBrandNameEN || apiCar.carBrandNameKM || 'Unknown',
    model: apiCar.carModelEN || apiCar.carModelKM || 'Unknown',
    year: apiCar.carYear || new Date().getFullYear(),
    price: apiCar.carPrice || 0,
    condition: apiCar.carConditionEN ? apiCar.carConditionEN.toLowerCase() : 'used',
    mileage: apiCar.carMileage || 0,
    fuel: apiCar.carFuelNameEN || apiCar.carFuelNameKM || 'Gasoline',
    transmission: apiCar.carTransmissionNameEN || apiCar.carTransmissionNameKM || 'Automatic',
    color: apiCar.carColorEN || apiCar.carColorKM || 'Unknown',
    description: apiCar.carDetailsEN || apiCar.carDetailsKM || 'No description available',
    images: [
      apiCar.carImageBanner || 'https://via.placeholder.com/400x300?text=No+Image'
    ],
    seller: {
      name: 'AICP Auto',
      phone: '023-xxx-xxx',
      email: '<EMAIL>'
    },
    location: apiCar.carBranchNameEN || apiCar.carBranchNameKM || 'Cambodia',
    carCode: apiCar.carCode || apiCar.carID,
    carCodeFromBCT: apiCar.carCodeFromBCT || '',
    carType: apiCar.carTypeEN || apiCar.carTypeKM || 'Car',
    carCC: apiCar.carCC || 0,
    viewer: apiCar.viewer || 0,
    createdAt: apiCar.create_time || new Date().toISOString(),
    updatedAt: apiCar.update_time || new Date().toISOString()
  }
}

export const carService = {
  getCars: async (filters = {}) => {
    try {
      console.log('Fetching cars from API...') // Debug log
      
      const response = await api.get('/getCarAll')
      console.log('Raw API response:', response) // Debug log
      console.log('Response data type:', typeof response.data) // Debug log
      console.log('Response data:', response.data) // Debug log
      
      // Handle different response formats
      let carsData = null
      
      // Check if response.data is an array
      if (Array.isArray(response.data)) {
        carsData = response.data
      }
      // Check if response.data has a data property that's an array
      else if (response.data && Array.isArray(response.data.data)) {
        carsData = response.data.data
      }
      // Check if response.data has a cars property that's an array
      else if (response.data && Array.isArray(response.data.cars)) {
        carsData = response.data.cars
      }
      // Check if response.data has a result property that's an array
      else if (response.data && Array.isArray(response.data.result)) {
        carsData = response.data.result
      }
      // If response.data is an object with unknown structure, try to find array
      else if (response.data && typeof response.data === 'object') {
        // Find the first array property in the response
        const arrayProperty = Object.keys(response.data).find(key => 
          Array.isArray(response.data[key])
        )
        if (arrayProperty) {
          carsData = response.data[arrayProperty]
        }
      }
      
      console.log('Extracted cars data:', carsData) // Debug log
      
      if (!carsData || !Array.isArray(carsData)) {
        console.error('Invalid response format. Expected array but got:', typeof carsData)
        console.error('Full response:', response.data)
        
        // Return empty array instead of throwing error for better UX
        return []
      }

      if (carsData.length === 0) {
        console.log('No cars found in API response')
        return []
      }

      console.log('Transforming', carsData.length, 'cars...') // Debug log
      
      let cars = carsData.map((car, index) => {
        try {
          return transformCarData(car)
        } catch (error) {
          console.error(`Error transforming car at index ${index}:`, error, car)
          return null
        }
      }).filter(car => car !== null) // Remove failed transformations
      
      console.log('Transformed cars:', cars.length) // Debug log
      
      // Apply filters
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        cars = cars.filter(car =>
          car.title.toLowerCase().includes(searchTerm) ||
          car.brand.toLowerCase().includes(searchTerm) ||
          car.model.toLowerCase().includes(searchTerm) ||
          car.carCode.toString().includes(searchTerm) ||
          car.carCodeFromBCT.toLowerCase().includes(searchTerm)
        )
      }
      
      if (filters.brand) {
        cars = cars.filter(car => 
          car.brand.toLowerCase() === filters.brand.toLowerCase()
        )
      }
      
      if (filters.minPrice) {
        cars = cars.filter(car => car.price >= parseInt(filters.minPrice))
      }
      
      if (filters.maxPrice) {
        cars = cars.filter(car => car.price <= parseInt(filters.maxPrice))
      }
      
      if (filters.year) {
        cars = cars.filter(car => car.year === parseInt(filters.year))
      }
      
      if (filters.condition) {
        cars = cars.filter(car => car.condition === filters.condition)
      }

      if (filters.carType) {
        cars = cars.filter(car => 
          car.carType.toLowerCase().includes(filters.carType.toLowerCase())
        )
      }

      if (filters.location) {
        cars = cars.filter(car => 
          car.location.toLowerCase().includes(filters.location.toLowerCase())
        )
      }
      
      // Sort by updated time (newest first)
      cars.sort((a, b) => new Date(b.updatedAt) - new Date(a.updatedAt))
      
      console.log('Final filtered cars:', cars.length) // Debug log
      return cars
      
    } catch (error) {
      console.error('Error fetching cars:', error)
      
      // More detailed error logging
      if (error.response) {
        console.error('Response status:', error.response.status)
        console.error('Response data:', error.response.data)
        console.error('Response headers:', error.response.headers)
      } else if (error.request) {
        console.error('Request made but no response:', error.request)
      } else {
        console.error('Error setting up request:', error.message)
      }
      
      // Return empty array instead of throwing for better UX
      return []
    }
  },

  getCarById: async (id) => {
    try {
      // Since we don't have single car endpoint, get all and filter
      const cars = await carService.getCars()
      const car = cars.find(car => car.id === parseInt(id))
      
      if (!car) {
        throw new Error('Car not found')
      }
      
      return car
    } catch (error) {
      console.error('Error fetching car:', error)
      throw new Error(`Failed to fetch car: ${error.message}`)
    }
  },

  // Create car (mock implementation since no API endpoint)
  createCar: async (carData) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const newCar = {
        id: Date.now(),
        ...carData,
        carCode: Date.now(),
        carCodeFromBCT: `AICP${Date.now()}`,
        viewer: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      
      return newCar
    } catch (error) {
      throw new Error(`Failed to create car: ${error.message}`)
    }
  },

  // Update car (mock implementation)
  updateCar: async (id, carData) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const updatedCar = {
        ...carData,
        id: parseInt(id),
        updatedAt: new Date().toISOString()
      }
      
      return updatedCar
    } catch (error) {
      throw new Error(`Failed to update car: ${error.message}`)
    }
  },

  // Delete car (mock implementation)
  deleteCar: async (id) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      return true
    } catch (error) {
      throw new Error(`Failed to delete car: ${error.message}`)
    }
  },

  // Get unique brands from API data
  getBrands: async () => {
    try {
      const cars = await carService.getCars()
      const brands = [...new Set(cars.map(car => car.brand).filter(Boolean))]
      return brands.sort()
    } catch (error) {
      console.error('Error fetching brands:', error)
      return []
    }
  },

  // Get unique locations from API data
  getLocations: async () => {
    try {
      const cars = await carService.getCars()
      const locations = [...new Set(cars.map(car => car.location).filter(Boolean))]
      return locations.sort()
    } catch (error) {
      console.error('Error fetching locations:', error)
      return []
    }
  },

  // Get unique car types from API data
  getCarTypes: async () => {
    try {
      const cars = await carService.getCars()
      const carTypes = [...new Set(cars.map(car => car.carType).filter(Boolean))]
      return carTypes.sort()
    } catch (error) {
      console.error('Error fetching car types:', error)
      return []
    }
  }
}