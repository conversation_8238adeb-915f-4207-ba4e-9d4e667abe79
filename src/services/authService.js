import axios from 'axios'

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api'

// สร้าง axios instance
const api = axios.create({
  baseURL: API_URL,
  timeout: 10000,
})

// Interceptor สำหรับแนบ token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Interceptor สำหรับจัดการ response
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export const authService = {
  // สำหรับ demo - ในจริงจะเป็น API call
  login: async (credentials) => {
    try {
      // Mock API call - แทนที่ด้วย API จริง
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // ตรวจสอบ credentials
      if (credentials.email === '<EMAIL>' && credentials.password === 'password') {
        return {
          token: 'mock-jwt-token-' + Date.now(),
          user: {
            id: 1,
            email: credentials.email,
            name: 'Admin User',
            role: 'admin'
          }
        }
      } else {
        throw new Error('Invalid credentials')
      }
      
      // ในจริงจะเป็น:
      // const response = await api.post('/auth/login', credentials)
      // return response.data
    } catch (error) {
      throw error
    }
  },

  validateToken: async (token) => {
    try {
      // Mock validation - แทนที่ด้วย API จริง
      await new Promise(resolve => setTimeout(resolve, 500))
      
      if (token.startsWith('mock-jwt-token-')) {
        return {
          id: 1,
          email: '<EMAIL>',
          name: 'Admin User',
          role: 'admin'
        }
      } else {
        throw new Error('Invalid token')
      }
      
      // ในจริงจะเป็น:
      // const response = await api.get('/auth/validate')
      // return response.data.user
    } catch (error) {
      throw error
    }
  },

  logout: async () => {
    try {
      // await api.post('/auth/logout')
      localStorage.removeItem('token')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }
}