// อัตราแลกเปลี่ยน
const USD_TO_KHR_RATE = 4100 // 1 USD = 4100 KHR (อัปเดตค่านี้ตามต้องการ)

export const convertUsdToKhr = (usdAmount) => {
  return Math.round(usdAmount * USD_TO_KHR_RATE)
}

export const formatPriceUsd = (priceInUsd) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(priceInUsd)
}

export const formatPriceKhr = (priceInUsd) => {
  const priceInKhr = convertUsdToKhr(priceInUsd)
  return new Intl.NumberFormat('km-KH').format(priceInKhr) + ' ៛'
}

export const formatPrice = (priceInUsd, currency = 'USD') => {
  if (currency === 'KHR') {
    return formatPriceKhr(priceInUsd)
  }
  return formatPriceUsd(priceInUsd)
}

export const formatPriceWithBoth = (priceInUsd) => {
  const priceInKhr = convertUsdToKhr(priceInUsd)
  
  const usdFormatted = new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(priceInUsd)
  
  const khrFormatted = new Intl.NumberFormat('km-KH').format(priceInKhr) + ' ៛'
  
  return {
    usd: usdFormatted,
    khr: khrFormatted,
    khrAmount: priceInKhr,
    usdAmount: priceInUsd
  }
}

export const formatDate = (dateString) => {
  if (!dateString) return 'N/A'
  
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

export const formatMileage = (mileage) => {
  if (!mileage) return '0'
  return new Intl.NumberFormat('en-US').format(mileage)
}

export const formatDateTime = (dateString) => {
  if (!dateString) return 'N/A'
  
  const date = new Date(dateString)
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export const formatCompactPrice = (priceInUsd, currency = 'USD') => {
  if (currency === 'KHR') {
    const priceInKhr = convertUsdToKhr(priceInUsd)
    if (priceInKhr >= 1000000) {
      return `${(priceInKhr / 1000000).toFixed(1)}M ៛`
    } else if (priceInKhr >= 1000) {
      return `${(priceInKhr / 1000).toFixed(0)}K ៛`
    } else {
      return `${priceInKhr} ៛`
    }
  } else {
    if (priceInUsd >= 1000000) {
      return `$${(priceInUsd / 1000000).toFixed(1)}M`
    } else if (priceInUsd >= 1000) {
      return `$${(priceInUsd / 1000).toFixed(0)}K`
    } else {
      return `$${priceInUsd}`
    }
  }
}

// สำหรับการแปลงกลับจาก KHR เป็น USD (สำหรับ filter)
export const convertKhrToUsd = (khrAmount) => {
  return Math.round(khrAmount / USD_TO_KHR_RATE)
}

export const getCurrentExchangeRate = () => {
  return USD_TO_KHR_RATE
}