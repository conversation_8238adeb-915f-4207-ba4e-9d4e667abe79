// utils/entryUtils.js

// Get all entry data from localStorage
export const getAllEntryData = () => {
  return {
    brands: JSON.parse(localStorage.getItem('car_brands') || '[]'),
    colors: JSON.parse(localStorage.getItem('car_colors') || '[]'),
    models: JSON.parse(localStorage.getItem('car_models') || '[]'),
    branches: JSON.parse(localStorage.getItem('car_branches') || '[]'),
    fuelTypes: JSON.parse(localStorage.getItem('car_fuel_types') || '[]'),
    transmissions: JSON.parse(localStorage.getItem('car_transmissions') || '[]'),
    carTypes: JSON.parse(localStorage.getItem('car_types') || '[]'),
    conditions: JSON.parse(localStorage.getItem('car_conditions') || '[]'),
    ccSizes: JSON.parse(localStorage.getItem('car_cc_sizes') || '[]')
  }
}

// Get specific entry data
export const getEntryData = (type) => {
  const keyMap = {
    brand: 'car_brands',
    color: 'car_colors',
    model: 'car_models',
    branch: 'car_branches',
    'fuel-type': 'car_fuel_types',
    transmission: 'car_transmissions',
    'car-type': 'car_types',
    condition: 'car_conditions',
    cc: 'car_cc_sizes'
  }
  
  const key = keyMap[type]
  return key ? JSON.parse(localStorage.getItem(key) || '[]') : []
}

// Save entry data
export const saveEntryData = (type, data) => {
  const keyMap = {
    brand: 'car_brands',
    color: 'car_colors',
    model: 'car_models',
    branch: 'car_branches',
    'fuel-type': 'car_fuel_types',
    transmission: 'car_transmissions',
    'car-type': 'car_types',
    condition: 'car_conditions',
    cc: 'car_cc_sizes'
  }
  
  const key = keyMap[type]
  if (key) {
    localStorage.setItem(key, JSON.stringify(data))
  }
}

// Initialize default data if not exists
export const initializeDefaultData = () => {
  const entryData = getAllEntryData()
  
  // Check if any data is missing and initialize
  Object.keys(entryData).forEach(key => {
    if (entryData[key].length === 0) {
      // Initialize with default data based on type
      // This can be expanded with default data for each type
    }
  })
}

// Export options for dropdowns
export const getEntryOptions = (type) => {
  const data = getEntryData(type)
  return data.map(item => ({
    value: item.id,
    label: item.name || item.value || item.title
  }))
}