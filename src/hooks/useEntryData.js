// hooks/useEntryData.js
import { useState, useEffect } from 'react'
import { getAllEntryData, getEntryData } from '../utils/entryUtils'

export const useAllEntryData = () => {
  const [entryData, setEntryData] = useState({})
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadData = () => {
      try {
        const data = getAllEntryData()
        setEntryData(data)
      } catch (error) {
        console.error('Error loading entry data:', error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [])

  return { entryData, loading }
}

export const useEntryData = (type) => {
  const [data, setData] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadData = () => {
      try {
        const entryData = getEntryData(type)
        setData(entryData)
      } catch (error) {
        console.error(`Error loading ${type} data:`, error)
      } finally {
        setLoading(false)
      }
    }

    loadData()
  }, [type])

  const refresh = () => {
    setLoading(true)
    const entryData = getEntryData(type)
    setData(entryData)
    setLoading(false)
  }

  return { data, loading, refresh }
}