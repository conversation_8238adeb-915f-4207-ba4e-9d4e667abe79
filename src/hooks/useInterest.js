// hooks/useInterest.js
import { useState, useEffect } from 'react'

const useInterest = () => {
  const [interestedCars, setInterestedCars] = useState([])

  // โหลดข้อมูลจาก localStorage เมื่อเริ่มต้น
  useEffect(() => {
    const savedInterests = localStorage.getItem('interestedCars')
    if (savedInterests) {
      try {
        setInterestedCars(JSON.parse(savedInterests))
      } catch (error) {
        console.error('Error parsing interested cars:', error)
        setInterestedCars([])
      }
    }
  }, [])

  // บันทึกลง localStorage เมื่อข้อมูลเปลี่ยน
  useEffect(() => {
    localStorage.setItem('interestedCars', JSON.stringify(interestedCars))
  }, [interestedCars])

  const addToInterest = (car) => {
    setInterestedCars(prev => {
      // ตรวจสอบว่ามีรถคันนี้อยู่แล้วหรือไม่
      const exists = prev.some(interestedCar => interestedCar.id === car.id)
      if (!exists) {
        return [...prev, {
          ...car,
          addedToInterestAt: new Date().toISOString()
        }]
      }
      return prev
    })
  }

  const removeFromInterest = (carId) => {
    setInterestedCars(prev => prev.filter(car => car.id !== carId))
  }

  const toggleInterest = (car) => {
    const isInterested = interestedCars.some(interestedCar => interestedCar.id === car.id)
    
    if (isInterested) {
      removeFromInterest(car.id)
      return false // ไม่ถูกใจแล้ว
    } else {
      addToInterest(car)
      return true // ถูกใจแล้ว
    }
  }

  const isCarInterested = (carId) => {
    return interestedCars.some(car => car.id === carId)
  }

  const getInterestedCarsCount = () => {
    return interestedCars.length
  }

  const clearAllInterests = () => {
    setInterestedCars([])
  }

  return {
    interestedCars,
    addToInterest,
    removeFromInterest,
    toggleInterest,
    isCarInterested,
    getInterestedCarsCount,
    clearAllInterests
  }
}

export default useInterest