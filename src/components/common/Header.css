.header {
  background: #1C466E;;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  border-bottom: 1px solid #e5e7eb;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 30px;
}

/* เพิ่มหรือแก้ไขส่วน logo */
.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logo:hover {
  transform: translateY(-1px);
}

.logo-image {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.logo-image:hover {
  transform: scale(1.05);
}

.logo:active .logo-image {
  transform: scale(0.95);
}

.logo-image.clickable {
  cursor: pointer;
}

.logo-text {
  color: #1a1a1a;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  width: 20px;
  height: 20px;
  color: #6b7280;
}

.search-input {
  width: 300px;
  height: 40px;
  padding: 0 16px 0 40px;
  border: 2px solid #e5e7eb;
  border-radius: 20px;
  font-size: 14px;
  transition: all 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.add-car-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.add-car-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.user-menu {
  position: relative;
}

.user-menu-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
}

.user-menu-trigger:hover {
  border-color: #667eea;
}

.user-menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 200px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  margin-top: 4px;
  z-index: 1000;
}

.user-info {
  padding: 12px 16px;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  margin: 0 0 4px 0;
}

.user-email {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

.user-menu-dropdown hr {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 0;
}

.menu-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: none;
  color: #374151;
  cursor: pointer;
  transition: background 0.2s;
}

.menu-item:hover {
  background: #f3f4f6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .header-container {
    padding: 0 16px;
  }
  
  .header-left {
    gap: 16px;
  }
  
  .logo-image {
    width: 36px;
    height: 36px;
  }
  
  .search-input {
    width: 200px;
  }
  
  .add-car-btn span {
    display: none;
  }
}

@media (max-width: 480px) {
  .header-left {
    gap: 12px;
  }
  
  .logo-image {
    width: 32px;
    height: 32px;
  }
  
  .logo-text {
    display: none;
  }
  
  .search-input {
    width: 150px;
  }
  
  .header-right {
    gap: 12px;
  }
  
  .user-menu-trigger span {
    display: none;
  }
}