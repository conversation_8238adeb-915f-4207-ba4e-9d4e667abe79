.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  width: 320px;
  height: 100vh;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

.sidebar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid #e2e8f0;
  background: linear-gradient(135deg, #1C466E 0%, #1C466E 100%);
  color: white;
}

.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-logo-image {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.sidebar-logo-text {
  font-size: 20px;
  font-weight: 700;
  color: white;
}

.sidebar-close {
  width: 36px;
  height: 36px;
  border: none;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.sidebar-close:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px 0;
}

.sidebar-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.sidebar-content::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

.sidebar-nav {
  padding: 0 16px;
}

.nav-item {
  margin-bottom: 4px;
}

.nav-link {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: none;
  background: none;
  color: #475569;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  font-weight: 500;
}

.nav-link:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  color: #334155;
  transform: translateX(4px);
}

.nav-link.has-submenu:hover {
  background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%);
  color: #5b21b6;
}

.nav-link-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: #667eea;
}

.nav-label {
  font-weight: 500;
}

.nav-arrow {
  color: #94a3b8;
  transition: transform 0.2s;
}

.submenu {
  margin-top: 8px;
  margin-left: 16px;
  padding-left: 16px;
  border-left: 2px solid #e2e8f0;
  animation: expandSubmenu 0.3s ease-out;
}

@keyframes expandSubmenu {
  from {
    opacity: 0;
    max-height: 0;
  }
  to {
    opacity: 1;
    max-height: 500px;
  }
}

.submenu-link {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 12px;
  border: none;
  background: none;
  color: #64748b;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
  margin-bottom: 2px;
}

.submenu-link:hover {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  transform: translateX(4px);
}

.submenu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  color: #f59e0b;
}

.submenu-label {
  font-weight: 500;
}

.sidebar-footer {
  padding: 16px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

.sidebar-user {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #667eea;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 2px;
}

.user-role {
  font-size: 12px;
  color: #64748b;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 280px;
  }
  
  .sidebar-header {
    padding: 16px 20px;
  }
  
  .sidebar-logo-text {
    font-size: 18px;
  }
  
  .nav-link {
    padding: 10px 12px;
    font-size: 13px;
  }
  
  .submenu-link {
    padding: 8px 10px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .sidebar {
    width: 100vw;
  }
  
  .sidebar-header {
    padding: 12px 16px;
  }
  
  .sidebar-content {
    padding: 12px 0;
  }
  
  .sidebar-nav {
    padding: 0 12px;
  }
}

/* Active States */
.nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.nav-link.active .nav-icon {
  color: white;
}

.submenu-link.active {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.submenu-link.active .submenu-icon {
  color: white;
}

/* Loading Animation */
.nav-link.loading {
  position: relative;
  pointer-events: none;
}

.nav-link.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 16px;
  width: 16px;
  height: 16px;
  border: 2px solid #e2e8f0;
  border-top: 2px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transform: translateY(-50%);
}

@keyframes spin {
  0% { transform: translateY(-50%) rotate(0deg); }
  100% { transform: translateY(-50%) rotate(360deg); }
}

/* Click outside to close */
.sidebar-overlay {
  backdrop-filter: blur(2px);
}

/* Smooth close animation */
.sidebar.closing {
  animation: slideOut 0.3s ease-out;
}

@keyframes slideOut {
  from { transform: translateX(0); }
  to { transform: translateX(-100%); }
}

.sidebar-overlay.closing {
  animation: fadeOut 0.3s ease-out;
}

@keyframes fadeOut {
  from { opacity: 1; }
  to { opacity: 0; }
}

/* Submenu collapse animation */
.submenu.closing {
  animation: collapseSubmenu 0.3s ease-out;
}

@keyframes collapseSubmenu {
  from {
    opacity: 1;
    max-height: 500px;
  }
  to {
    opacity: 0;
    max-height: 0;
  }
}

/* Focus states for accessibility */
.nav-link:focus,
.submenu-link:focus,
.sidebar-close:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

/* Menu item indicators */
.nav-link.has-notification::after {
  content: '';
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  background: #ef4444;
  border-radius: 50%;
  border: 2px solid white;
}

/* Submenu counter */
.nav-link .submenu-count {
  background: #f1f5f9;
  color: #64748b;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: auto;
  margin-right: 8px;
}

.nav-link:hover .submenu-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* Divider between menu sections */
.nav-divider {
  height: 1px;
  background: #e2e8f0;
  margin: 8px 16px;
}

/* Menu section headers */
.nav-section-header {
  padding: 8px 16px;
  color: #94a3b8;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Theme Toggle Section */
.theme-toggle-section {
  margin: 20px 16px;
  padding: 16px 0;
  border-top: 1px solid #e2e8f0;
}

.theme-toggle-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.theme-toggle-btn:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.theme-toggle-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.theme-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  color: #667eea;
}

.theme-label {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.theme-switch {
  width: 44px;
  height: 24px;
  background: #cbd5e1;
  border-radius: 12px;
  position: relative;
  transition: all 0.3s;
}

.theme-switch.dark {
  background: #667eea;
}

.theme-switch-handle {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.3s;
}

.theme-switch.dark .theme-switch-handle {
  transform: translateX(20px);
}

/* Dark Mode Styles for Sidebar */
.dark .sidebar {
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  border-right: 1px solid #334155;
}

.dark .sidebar-header {
  background: linear-gradient(135deg, #4338ca 0%, #5b21b6 100%);
  border-bottom: 1px solid #334155;
}

.dark .sidebar-close {
  background: rgba(255, 255, 255, 0.1);
}

.dark .sidebar-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

.dark .nav-link {
  color: #cbd5e1;
}

.dark .nav-link:hover {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
  color: #f1f5f9;
}

.dark .nav-link.has-submenu:hover {
  background: linear-gradient(135deg, #4c1d95 0%, #5b21b6 100%);
  color: #f3e8ff;
}

.dark .nav-icon {
  color: #818cf8;
}

.dark .submenu {
  border-left-color: #475569;
}

.dark .submenu-link {
  color: #94a3b8;
}

.dark .submenu-link:hover {
  background: linear-gradient(135deg, #292524 0%, #44403c 100%);
  color: #fbbf24;
}

.dark .submenu-icon {
  color: #fbbf24;
}

.dark .theme-toggle-btn {
  background: #1e293b;
  border-color: #334155;
}

.dark .theme-toggle-btn:hover {
  background: #334155;
  border-color: #475569;
}

.dark .theme-icon-wrapper {
  background: #334155;
  color: #fbbf24;
}

.dark .theme-label {
  color: #e2e8f0;
}

.dark .theme-toggle-section {
  border-top-color: #334155;
}

.dark .sidebar-footer {
  background: #0f172a;
  border-top-color: #334155;
}

.dark .sidebar-user {
  background: #1e293b;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

.dark .user-name {
  color: #f1f5f9;
}

.dark .user-role {
  color: #94a3b8;
}

.dark .nav-arrow {
  color: #64748b;
}

.dark .nav-divider {
  background: #334155;
}

.dark .nav-section-header {
  color: #64748b;
}