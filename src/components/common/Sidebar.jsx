import { useState } from 'react'
import {
    Home,
    Plus,
    Database,
    Tag,
    Palette,
    Car,
    MapPin,
    Fuel,
    Settings,
    Truck,
    Star,
    Gauge,
    Gift,
    Heart,
    ChevronDown,
    ChevronRight,
    X,
    Sun,
    Moon
} from 'lucide-react'
import { useTheme } from '../../context/ThemeContext'
import { useNavigate } from 'react-router-dom'
import './Sidebar.css'

const Sidebar = ({ isOpen, onClose }) => {
    const [expandedMenu, setExpandedMenu] = useState(null)
    const { isDarkMode, toggleTheme } = useTheme()
    const navigate = useNavigate()

    const toggleSubmenu = (menuKey) => {
        setExpandedMenu(expandedMenu === menuKey ? null : menuKey)
    }

    const menuItems = [
        {
            key: 'home',
            label: 'Home',
            icon: <Home size={20} />,
            path: '/dashboard'
        },
        {
            key: 'add-car',
            label: 'Add Car',
            icon: <Plus size={20} />,
            action: 'add-car'
        },
        {
            key: 'entry',
            label: 'Entry',
            icon: <Database size={20} />,
            hasSubmenu: true,
            submenu: [
                { key: 'brand', label: 'Brand', icon: <Tag size={16} /> },
                { key: 'color', label: 'Color', icon: <Palette size={16} /> },
                { key: 'model', label: 'Model', icon: <Car size={16} /> },
                { key: 'branch', label: 'Branch', icon: <MapPin size={16} /> },
                { key: 'fuel-type', label: 'Fuel Type', icon: <Fuel size={16} /> },
                { key: 'transmission', label: 'Transmission', icon: <Settings size={16} /> },
                { key: 'car-type', label: 'Car Type', icon: <Truck size={16} /> },
                { key: 'condition', label: 'Condition', icon: <Star size={16} /> },
                { key: 'cc', label: 'CC', icon: <Gauge size={16} /> }
            ]
        },
        {
            key: 'promotion',
            label: 'Promotion',
            icon: <Gift size={20} />,
            path: '/promotion'
        },
        {
            key: 'interest',
            label: 'Interest',
            icon: <Heart size={20} />,
            path: '/interest'
        }
    ]

    const handleMenuClick = (item) => {
        if (item.hasSubmenu) {
            toggleSubmenu(item.key)
        } else if (item.action === 'add-car') {
            window.dispatchEvent(new CustomEvent('openAddCarForm'))
            onClose()
        } else if (item.path) {
            navigate(item.path)
            onClose()
        }
    }

    // อัปเดตฟังก์ชัน handleSubmenuClick ใน Sidebar.js
    const handleSubmenuClick = (parentKey, submenuItem) => {
        if (parentKey === 'entry') {
            navigate(`/entry/${submenuItem.key}`)
        } else {
            console.log('Submenu clicked:', parentKey, submenuItem.key)
        }
        onClose()
    }

    if (!isOpen) return null

    return (
        <div className="sidebar-overlay" onClick={onClose}>
            <div className="sidebar" onClick={(e) => e.stopPropagation()}>
                <div className="sidebar-header">
                    <div className="sidebar-logo">
                        <img
                            src="/images/AICP_Logo.jpg"
                            alt="AICP Logo"
                            className="sidebar-logo-image"
                        />
                        <span className="sidebar-logo-text">AICP Motors</span>
                    </div>
                    <button className="sidebar-close" onClick={onClose}>
                        <X size={24} />
                    </button>
                </div>

                <div className="sidebar-content">
                    <nav className="sidebar-nav">
                        {menuItems.map((item) => (
                            <div key={item.key} className="nav-item">
                                <button
                                    className={`nav-link ${item.hasSubmenu ? 'has-submenu' : ''}`}
                                    onClick={() => handleMenuClick(item)}
                                >
                                    <div className="nav-link-content">
                                        <div className="nav-icon">{item.icon}</div>
                                        <span className="nav-label">{item.label}</span>
                                    </div>
                                    {item.hasSubmenu && (
                                        <div className="nav-arrow">
                                            {expandedMenu === item.key ?
                                                <ChevronDown size={16} /> :
                                                <ChevronRight size={16} />
                                            }
                                        </div>
                                    )}
                                </button>

                                {item.hasSubmenu && expandedMenu === item.key && (
                                    <div className="submenu">
                                        {item.submenu.map((subItem) => (
                                            <button
                                                key={subItem.key}
                                                className="submenu-link"
                                                onClick={() => handleSubmenuClick(item.key, subItem)}
                                            >
                                                <div className="submenu-icon">{subItem.icon}</div>
                                                <span className="submenu-label">{subItem.label}</span>
                                            </button>
                                        ))}
                                    </div>
                                )}
                            </div>
                        ))}
                    </nav>

                    <div className="theme-toggle-section">
                        <button
                            className="theme-toggle-btn"
                            onClick={toggleTheme}
                            title={isDarkMode ? "Switch to light mode" : "Switch to dark mode"}
                        >
                            <div className="theme-toggle-content">
                                <div className="theme-icon-wrapper">
                                    {isDarkMode ? <Moon size={20} /> : <Sun size={20} />}
                                </div>
                                <span className="theme-label">
                                    {isDarkMode ? 'Dark Mode' : 'Light Mode'}
                                </span>
                            </div>
                            <div className={`theme-switch ${isDarkMode ? 'dark' : 'light'}`}>
                                <div className="theme-switch-handle"></div>
                            </div>
                        </button>
                    </div>
                </div>

                <div className="sidebar-footer">
                    <div className="sidebar-user">
                        <div className="user-avatar">
                            <img
                                src="https://via.placeholder.com/40x40?text=U"
                                alt="User Avatar"
                            />
                        </div>
                        <div className="user-info">
                            <div className="user-name">Admin User</div>
                            <div className="user-role">Administrator</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Sidebar