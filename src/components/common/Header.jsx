import { useState, useEffect } from 'react'
import { useAuth } from '../../context/AuthContext'
import { useCarContext } from '../../context/CarContext'
import { Search, Plus, User, LogOut } from 'lucide-react'
import Sidebar from './Sidebar'
import './Header.css'

const Header = ({ onAddCar }) => {
    const { user, logout } = useAuth()
    const { filters, setFilters } = useCarContext()
    const [showUserMenu, setShowUserMenu] = useState(false)
    const [sidebarOpen, setSidebarOpen] = useState(false)

    const handleSearch = (e) => {
        setFilters(prev => ({ ...prev, search: e.target.value }))
    }

    const handleLogoClick = () => {
        setSidebarOpen(true)
    }

    const handleCloseSidebar = () => {
        setSidebarOpen(false)
    }

    const handleAddCarClick = () => {
        if (onAddCar) {
            onAddCar()
        }
    }

    // Listen for custom event from sidebar
    useEffect(() => {
        const handleOpenAddCarForm = () => {
            if (onAddCar) {
                onAddCar()
            }
        }

        window.addEventListener('openAddCarForm', handleOpenAddCarForm)
        return () => {
            window.removeEventListener('openAddCarForm', handleOpenAddCarForm)
        }
    }, [onAddCar])

    // Close user menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (showUserMenu && !event.target.closest('.user-menu')) {
                setShowUserMenu(false)
            }
        }

        document.addEventListener('mousedown', handleClickOutside)
        return () => {
            document.removeEventListener('mousedown', handleClickOutside)
        }
    }, [showUserMenu])

    return (
        <>
            <header className="header">
                <div className="header-container">
                    <div className="header-left">
                        <div className="" onClick={handleLogoClick}>
                            <img
                                src="/images/AICP_Logo.jpg"
                                alt="AICP Logo"
                                className="logo-image clickable"
                            />
                        </div>

                        <div className="search-container">
                            <Search className="search-icon" />
                            <input
                                type="text"
                                placeholder="Sreach..."
                                value={filters.search}
                                onChange={handleSearch}
                                className="search-input"
                            />
                        </div>
                    </div>

                    <div className="header-right">
                        <div className="user-menu">
                            <button
                                className="user-menu-trigger"
                                onClick={() => setShowUserMenu(!showUserMenu)}
                            >
                                <User size={20} />
                                <span>{user?.name}</span>
                            </button>

                            {showUserMenu && (
                                <div className="user-menu-dropdown">
                                    <div className="user-info">
                                        <p className="user-name">{user?.name}</p>
                                        <p className="user-email">{user?.email}</p>
                                    </div>
                                    <hr />
                                    <button className="menu-item" onClick={logout}>
                                        <LogOut size={16} />
                                        <span>ออกจากระบบ</span>
                                    </button>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </header>

            <Sidebar
                isOpen={sidebarOpen}
                onClose={handleCloseSidebar}
            />
        </>
    )
}

export default Header