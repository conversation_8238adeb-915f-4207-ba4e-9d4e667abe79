/* Interest.css */
.interest-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.interest-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.interest-title-section h1 {
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 8px 0;
}

.interest-title-section p {
  color: #666;
  margin: 0;
  font-size: 16px;
}

.interest-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.interest-sort {
  display: flex;
  align-items: center;
  gap: 8px;
}

.interest-sort label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.interest-sort-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #333;
  cursor: pointer;
}

.interest-sort-select:focus {
  outline: none;
  border-color: #1C466E;
  box-shadow: 0 0 0 3px rgba(28, 70, 110, 0.1);
}

.interest-view-toggle {
  display: flex;
  background: #f5f5f5;
  border-radius: 6px;
  padding: 2px;
}

.interest-view-btn {
  padding: 8px 12px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  color: #666;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.interest-view-btn:hover {
  background: #e0e0e0;
}

.interest-view-btn.active {
  background: #1C466E;
  color: white;
}

.interest-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.interest-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1C466E;
  border-radius: 50%;
  animation: interest-spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes interest-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.interest-empty {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.interest-empty-icon {
  margin-bottom: 20px;
  color: #ccc;
}

.interest-empty h3 {
  font-size: 24px;
  margin-bottom: 12px;
  color: #333;
}

.interest-empty p {
  font-size: 16px;
  margin-bottom: 30px;
  color: #666;
}

.interest-browse-btn {
  padding: 12px 24px;
  background: #1C466E;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.interest-browse-btn:hover {
  background: #2a5885;
  transform: translateY(-2px);
}

.interest-grid {
  display: grid;
  gap: 24px;
}

.interest-grid.grid {
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

.interest-grid.list {
  grid-template-columns: 1fr;
}

.interest-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #eee;
}

.interest-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.interest-card-image {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.interest-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.interest-card:hover .interest-card-image img {
  transform: scale(1.05);
}

.interest-remove-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #e74c3c;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.interest-remove-btn:hover {
  background: #e74c3c;
  color: white;
  transform: scale(1.1);
}

.interest-card-content {
  padding: 20px;
}

.interest-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.interest-car-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
  flex: 1;
}

.interest-condition-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.interest-condition-badge.new {
  background: #d4edda;
  color: #155724;
}

.interest-condition-badge.used {
  background: #fff3cd;
  color: #856404;
}

.interest-condition-badge.like-new {
  background: #cce5ff;
  color: #004085;
}

.interest-price-section {
  margin-bottom: 16px;
}

.interest-price {
  font-size: 24px;
  font-weight: 700;
  color: #1C466E;
  margin-bottom: 4px;
}

.interest-original-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.interest-crossed-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.interest-discount {
  font-size: 12px;
  background: #e74c3c;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

.interest-car-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 16px;
}

.interest-detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #666;
}

.interest-detail-item svg {
  color: #1C466E;
  flex-shrink: 0;
}

.interest-card-meta {
  margin-bottom: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.interest-seller-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.interest-seller-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.interest-location {
  font-size: 14px;
  color: #666;
}

.interest-added-date {
  font-size: 12px;
  color: #999;
}

.interest-card-actions {
  display: flex;
  gap: 12px;
}

.interest-action-btn {
  flex: 1;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
}

.interest-view-btn-action {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #ddd;
}

.interest-view-btn-action:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.interest-contact-btn {
  background: #1C466E;
  color: white;
}

.interest-contact-btn:hover {
  background: #2a5885;
  transform: translateY(-1px);
}

/* List view adjustments */
.interest-grid.list .interest-card {
  display: flex;
  height: 200px;
}

.interest-grid.list .interest-card-image {
  width: 300px;
  height: 100%;
  flex-shrink: 0;
}

.interest-grid.list .interest-card-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.interest-grid.list .interest-car-details {
  grid-template-columns: repeat(4, 1fr);
}

/* Responsive design */
@media (max-width: 768px) {
  .interest-container {
    padding: 16px;
  }
  
  .interest-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .interest-controls {
    justify-content: space-between;
  }
  
  .interest-grid.grid {
    grid-template-columns: 1fr;
  }
  
  .interest-grid.list .interest-card {
    flex-direction: column;
    height: auto;
  }
  
  .interest-grid.list .interest-card-image {
    width: 100%;
    height: 200px;
  }
  
  .interest-grid.list .interest-car-details {
    grid-template-columns: 1fr 1fr;
  }
  
  .interest-card-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .interest-title-section h1 {
    font-size: 24px;
  }
  
  .interest-car-details {
    grid-template-columns: 1fr;
  }
  
  .interest-seller-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* เพิ่มใน Interest.css */
.clear-all-btn {
  padding: 8px 16px;
  background: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-all-btn:hover {
  background: #dc2626;
  transform: translateY(-1px);
}

.interest-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}