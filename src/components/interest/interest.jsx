// pages/Interest/Interest.js
import React, { useState } from 'react'
import { Eye, Edit, Trash2, Heart, MapPin, Calendar, Gauge, DollarSign } from 'lucide-react'
import { formatPriceWithBoth, formatDate } from '../../utils/formatters'
import { useInterest } from '../../context/InterestContext'
import './interest.css'

const Interest = ({ onViewCar, onEditCar }) => {
  const [viewMode, setViewMode] = useState('grid')
  const [sortBy, setSortBy] = useState('newest')
  
  const { interestedCars, removeFromInterest, clearAllInterests, loading } = useInterest()

  console.log('Interest page - interestedCars:', interestedCars) // Debug

  const handleRemoveInterest = (carId) => {
    if (window.confirm('คุณต้องการลบรถคันนี้ออกจากรายการที่สนใจใช่หรือไม่?')) {
      const car = interestedCars.find(c => c.id === carId)
      removeFromInterest(carId)
      
      if (car) {
        alert(`ลบ "${car.title}" ออกจากรายการที่สนใจแล้ว`)
      }
    }
  }

  const handleClearAll = () => {
    if (window.confirm('คุณต้องการลบรถทั้งหมดออกจากรายการที่สนใจใช่หรือไม่?')) {
      clearAllInterests()
      alert('ลบรถทั้งหมดออกจากรายการที่สนใจแล้ว')
    }
  }

  const handleContactSeller = (car) => {
    alert(`เปิดช่องทางติดต่อผู้ขาย: ${car.seller.name}`)
  }

  const sortedCars = [...interestedCars].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.addedToInterestAt || b.createdAt) - new Date(a.addedToInterestAt || a.createdAt)
      case 'oldest':
        return new Date(a.addedToInterestAt || a.createdAt) - new Date(b.addedToInterestAt || b.createdAt)
      case 'price_low':
        return a.price - b.price
      case 'price_high':
        return b.price - a.price
      default:
        return 0
    }
  })

  if (loading) {
    return (
      <div className="interest-loading">
        <div className="interest-spinner"></div>
        <p>กำลังโหลดรายการรถที่สนใจ...</p>
      </div>
    )
  }

  return (
    <div className="interest-container">
      <div className="interest-header">
        <div className="interest-title-section">
          <h1>รถที่ฉันสนใจ</h1>
          <p>รายการรถที่คุณได้เพิ่มเข้าสู่รายการโปรด ({interestedCars.length} คัน)</p>
        </div>
        
        {interestedCars.length > 0 && (
          <div className="interest-controls">
            <button 
              className="clear-all-btn"
              onClick={handleClearAll}
            >
              ลบทั้งหมด
            </button>
            
            <div className="interest-sort">
              <label>เรียงตาม:</label>
              <select 
                value={sortBy} 
                onChange={(e) => setSortBy(e.target.value)}
                className="interest-sort-select"
              >
                <option value="newest">เพิ่มล่าสุด</option>
                <option value="oldest">เพิ่มเก่าสุด</option>
                <option value="price_low">ราคาต่ำ-สูง</option>
                <option value="price_high">ราคาสูง-ต่ำ</option>
              </select>
            </div>
            
            <div className="interest-view-toggle">
              <button 
                className={`interest-view-btn ${viewMode === 'grid' ? 'active' : ''}`}
                onClick={() => setViewMode('grid')}
              >
                <svg width="16" height="16" viewBox="0 0 16 16">
                  <rect x="1" y="1" width="6" height="6" fill="currentColor"/>
                  <rect x="9" y="1" width="6" height="6" fill="currentColor"/>
                  <rect x="1" y="9" width="6" height="6" fill="currentColor"/>
                  <rect x="9" y="9" width="6" height="6" fill="currentColor"/>
                </svg>
              </button>
              <button 
                className={`interest-view-btn ${viewMode === 'list' ? 'active' : ''}`}
                onClick={() => setViewMode('list')}
              >
                <svg width="16" height="16" viewBox="0 0 16 16">
                  <rect x="1" y="2" width="14" height="2" fill="currentColor"/>
                  <rect x="1" y="7" width="14" height="2" fill="currentColor"/>
                  <rect x="1" y="12" width="14" height="2" fill="currentColor"/>
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>

      {interestedCars.length === 0 ? (
        <div className="interest-empty">
          <div className="interest-empty-icon">
            <Heart size={60} />
          </div>
          <h3>ยังไม่มีรถที่สนใจ</h3>
          <p>เริ่มเพิ่มรถที่คุณสนใจเพื่อติดตามได้ง่ายขึ้น</p>
          <button 
            className="interest-browse-btn"
            onClick={() => window.location.href = '/cars'}
          >
            เริ่มค้นหารถ
          </button>
        </div>
      ) : (
        <div className={`interest-grid ${viewMode}`}>
          {sortedCars.map(car => (
            <InterestCard 
              key={car.id}
              car={car}
              onRemove={handleRemoveInterest}
              onView={onViewCar}
              onEdit={onEditCar}
              onContact={handleContactSeller}
            />
          ))}
        </div>
      )}
    </div>
  )
}

// Component สำหรับแสดงรถในหน้า Interest
const InterestCard = ({ car, onRemove, onView, onEdit, onContact }) => {
  const [showKhrPrice, setShowKhrPrice] = useState(false)
  const priceInfo = formatPriceWithBoth(car.price)

  return (
    <div className="interest-card">
      <div className="interest-card-image">
        <img src={car.images[0]} alt={car.title} />
        <button 
          className="interest-remove-btn"
          onClick={() => onRemove(car.id)}
          title="ลบออกจากรายการ"
        >
          <Heart size={16} fill="currentColor" />
        </button>
      </div>
      
      <div className="interest-card-content">
        <div className="interest-card-header">
          <h3 className="interest-car-title">{car.title}</h3>
          <span className={`interest-condition-badge ${car.condition}`}>
            {car.condition === 'new' ? 'New' : 'Used'}
          </span>
        </div>
        
        <div className="interest-price-section">
          <div 
            className="interest-price"
            onClick={() => setShowKhrPrice(!showKhrPrice)}
            title={showKhrPrice ? "Click to show USD price" : "Click to show Riel price"}
          >
            {showKhrPrice ? priceInfo.khr : priceInfo.usd}
            <DollarSign 
              size={16} 
              className={`price-toggle ${showKhrPrice ? 'khr' : 'usd'}`}
            />
          </div>
        </div>
        
        <div className="interest-car-details">
          <div className="interest-detail-item">
            <Calendar size={14} />
            <span>{car.year}</span>
          </div>
          <div className="interest-detail-item">
            <Gauge size={14} />
            <span>{car.mileage.toLocaleString()} km</span>
          </div>
          <div className="interest-detail-item">
            <MapPin size={14} />
            <span>{car.location}</span>
          </div>
        </div>
        
        <div className="interest-card-meta">
          <div className="interest-seller-info">
            <span className="interest-seller-name">โดย {car.seller.name}</span>
          </div>
          <div className="interest-added-date">
            เพิ่มเมื่อ {formatDate(car.addedToInterestAt || car.createdAt)}
          </div>
        </div>
        
        <div className="interest-card-actions">
          <button 
            className="interest-action-btn interest-view-btn-action"
            onClick={() => onView && onView(car)}
          >
            <Eye size={14} />
            ดูรายละเอียด
          </button>
          <button 
            className="interest-action-btn interest-contact-btn"
            onClick={() => onContact(car)}
          >
            ติดต่อผู้ขาย
          </button>
        </div>
      </div>
    </div>
  )
}

export default Interest