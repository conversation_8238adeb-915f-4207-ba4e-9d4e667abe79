import { useState } from 'react'
import { useCarContext } from '../../context/CarContext'
import { convertKhrToUsd, convertUsdToKhr, getCurrentExchangeRate } from '../../utils/formatters'
import { ToggleLeft, ToggleRight } from 'lucide-react'
import './FilterPanel.css'

const FilterPanel = () => {
  const { filters, setFilters, brands, locations, carTypes } = useCarContext()
  const [priceInKhr, setPriceInKhr] = useState(false) // เริ่มต้นด้วย USD

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handlePriceChange = (key, value) => {
    if (priceInKhr) {
      // แปลงจาก KHR เป็น USD สำหรับการ filter
      const usdValue = value ? convertKhrToUsd(parseInt(value)) : ''
      setFilters(prev => ({
        ...prev,
        [key]: usdValue
      }))
    } else {
      // ใช้ USD โดยตรง
      setFilters(prev => ({
        ...prev,
        [key]: value
      }))
    }
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      brand: '',
      minPrice: '',
      maxPrice: '',
      year: '',
      condition: '',
      carType: '',
      location: ''
    })
  }

  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 30 }, (_, i) => currentYear - i)
  const exchangeRate = getCurrentExchangeRate()

  // แปลงค่า USD filter เป็น KHR สำหรับแสดงใน input
  const minPriceDisplay = priceInKhr && filters.minPrice ? 
    convertUsdToKhr(filters.minPrice) : filters.minPrice
  const maxPriceDisplay = priceInKhr && filters.maxPrice ? 
    convertUsdToKhr(filters.maxPrice) : filters.maxPrice

  return (
    <div className="filter-panel">
      <div className="filter-header">
        <h3>Filters</h3>
        <button onClick={clearFilters} className="clear-btn">
          Clear All
        </button>
      </div>

      <div className="filter-group">
        <label>Brand</label>
        <select
          value={filters.brand}
          onChange={(e) => handleFilterChange('brand', e.target.value)}
        >
          <option value="">All Brands</option>
          {brands.map(brand => (
            <option key={brand} value={brand}>{brand}</option>
          ))}
        </select>
      </div>

      <div className="filter-group">
        <label>Car Type</label>
        <select
          value={filters.carType}
          onChange={(e) => handleFilterChange('carType', e.target.value)}
        >
          <option value="">All Types</option>
          {carTypes.map(type => (
            <option key={type} value={type}>{type}</option>
          ))}
        </select>
      </div>

      <div className="filter-group">
        <label>Location</label>
        <select
          value={filters.location}
          onChange={(e) => handleFilterChange('location', e.target.value)}
        >
          <option value="">All Locations</option>
          {locations.map(location => (
            <option key={location} value={location}>{location}</option>
          ))}
        </select>
      </div>

      <div className="filter-group">
        <label>Condition</label>
        <select
          value={filters.condition}
          onChange={(e) => handleFilterChange('condition', e.target.value)}
        >
          <option value="">All Conditions</option>
          <option value="new">New</option>
          <option value="used">Used</option>
        </select>
      </div>

      <div className="filter-group">
        <label>Year</label>
        <select
          value={filters.year}
          onChange={(e) => handleFilterChange('year', e.target.value)}
        >
          <option value="">All Years</option>
          {years.map(year => (
            <option key={year} value={year}>{year}</option>
          ))}
        </select>
      </div>

      <div className="filter-group">
        <div className="price-filter-header">
          <label>Price Range</label>
          <button 
            className="currency-toggle-small"
            onClick={() => setPriceInKhr(!priceInKhr)}
            title={priceInKhr ? "Switch to USD" : "Switch to Riel"}
          >
            {priceInKhr ? <ToggleRight size={16} /> : <ToggleLeft size={16} />}
            <span>{priceInKhr ? 'KHR' : 'USD'}</span>
          </button>
        </div>
        <div className="price-range">
          <input
            type="number"
            placeholder={priceInKhr ? "Min (Riel)" : "Min (USD)"}
            value={minPriceDisplay}
            onChange={(e) => handlePriceChange('minPrice', e.target.value)}
          />
          <span>-</span>
          <input
            type="number"
            placeholder={priceInKhr ? "Max (Riel)" : "Max (USD)"}
            value={maxPriceDisplay}
            onChange={(e) => handlePriceChange('maxPrice', e.target.value)}
          />
        </div>
        <div className="price-examples">
          <small>
            {priceInKhr ? 
              'Example: 50,000,000, 100,000,000 Riel' : 
              'Example: 10,000, 25,000 USD'
            }
          </small>
          <small className="exchange-note">
            1 USD = {exchangeRate.toLocaleString()} KHR
          </small>
        </div>
      </div>
    </div>
  )
}

export default FilterPanel