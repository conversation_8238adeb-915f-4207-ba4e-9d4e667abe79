import { useState, useEffect } from 'react'
import { X, Upload, Plus, Minus } from 'lucide-react'
import './CarForm.css'

const CarForm = ({ car, onSave, onCancel, loading }) => {
  const [formData, setFormData] = useState({
    title: '',
    brand: '',
    model: '',
    year: new Date().getFullYear(),
    price: '',
    condition: 'used',
    mileage: '',
    fuel: 'Gasoline',
    transmission: 'Automatic',
    color: '',
    description: '',
    images: [''],
    seller: {
      name: '',
      phone: '',
      email: ''
    },
    location: ''
  })

  const [errors, setErrors] = useState({})

  useEffect(() => {
    if (car) {
      setFormData(car)
    }
  }, [car])

  const handleChange = (e) => {
    const { name, value } = e.target
    
    if (name.includes('.')) {
      const [parent, child] = name.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleImageChange = (index, value) => {
    const newImages = [...formData.images]
    newImages[index] = value
    setFormData(prev => ({ ...prev, images: newImages }))
  }

  const addImageField = () => {
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, '']
    }))
  }

  const removeImageField = (index) => {
    if (formData.images.length > 1) {
      const newImages = formData.images.filter((_, i) => i !== index)
      setFormData(prev => ({ ...prev, images: newImages }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.title) newErrors.title = 'Please enter the car title'
    if (!formData.brand) newErrors.brand = 'Please select a brand'
    if (!formData.model) newErrors.model = 'Please enter the model'
    if (!formData.price) newErrors.price = 'Please enter the price'
    if (!formData.color) newErrors.color = 'Please enter the color'
    if (!formData.description) newErrors.description = 'Please enter the description'
    if (!formData.seller.name) newErrors['seller.name'] = 'Please enter seller name'
    if (!formData.seller.phone) newErrors['seller.phone'] = 'Please enter phone number'
    if (!formData.location) newErrors.location = 'Please enter the location'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    if (validateForm()) {
      const cleanedData = {
        ...formData,
        images: formData.images.filter(img => img.trim() !== ''),
        price: parseInt(formData.price),
        mileage: parseInt(formData.mileage) || 0,
        year: parseInt(formData.year)
      }
      onSave(cleanedData)
    }
  }

  const brandOptions = [
    'Toyota', 'Honda', 'Nissan', 'Mazda', 'Mitsubishi', 'Isuzu',
    'BMW', 'Mercedes-Benz', 'Audi', 'Volkswagen', 'Ford', 'Chevrolet'
  ]

  return (
    <div className="car-form-container">
      <div className="car-form-header">
        <h2>{car ? 'Edit Car Info' : 'List a New Car'}</h2>
        <button className="close-btn" onClick={onCancel}>
          <X size={24} />
        </button>
      </div>

      <form onSubmit={handleSubmit} className="car-form">
        <div className="form-section">
          <h3>Car Information</h3>
          
          <div className="form-row">
            <div className="form-group">
              <label>Car Title *</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className={errors.title ? 'error' : ''}
                placeholder="e.g. Toyota Camry 2022"
              />
              {errors.title && <span className="error-text">{errors.title}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Brand *</label>
              <select
                name="brand"
                value={formData.brand}
                onChange={handleChange}
                className={errors.brand ? 'error' : ''}
              >
                <option value="">Select a brand</option>
                {brandOptions.map(brand => (
                  <option key={brand} value={brand}>{brand}</option>
                ))}
              </select>
              {errors.brand && <span className="error-text">{errors.brand}</span>}
            </div>

            <div className="form-group">
              <label>Model *</label>
              <input
                type="text"
                name="model"
                value={formData.model}
                onChange={handleChange}
                className={errors.model ? 'error' : ''}
                placeholder="e.g. Camry"
              />
              {errors.model && <span className="error-text">{errors.model}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Year</label>
              <input
                type="number"
                name="year"
                value={formData.year}
                onChange={handleChange}
                min="1990"
                max={new Date().getFullYear()}
              />
            </div>

            <div className="form-group">
              <label>Price (THB) *</label>
              <input
                type="number"
                name="price"
                value={formData.price}
                onChange={handleChange}
                className={errors.price ? 'error' : ''}
                placeholder="0"
              />
              {errors.price && <span className="error-text">{errors.price}</span>}
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Condition</label>
              <select
                name="condition"
                value={formData.condition}
                onChange={handleChange}
              >
                <option value="new">New</option>
                <option value="used">Used</option>
              </select>
            </div>

            <div className="form-group">
              <label>Mileage</label>
              <input
                type="number"
                name="mileage"
                value={formData.mileage}
                onChange={handleChange}
                placeholder="0"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Fuel Type</label>
              <select
                name="fuel"
                value={formData.fuel}
                onChange={handleChange}
              >
                <option value="Gasoline">Gasoline</option>
                <option value="Diesel">Diesel</option>
                <option value="Hybrid">Hybrid</option>
                <option value="Electric">Electric</option>
              </select>
            </div>

            <div className="form-group">
              <label>Transmission</label>
              <select
                name="transmission"
                value={formData.transmission}
                onChange={handleChange}
              >
                <option value="Automatic">Automatic</option>
                <option value="Manual">Manual</option>
              </select>
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label>Color *</label>
              <input
                type="text"
                name="color"
                value={formData.color}
                onChange={handleChange}
                className={errors.color ? 'error' : ''}
                placeholder="e.g. White, Black, Gray"
              />
              {errors.color && <span className="error-text">{errors.color}</span>}
            </div>

            <div className="form-group">
              <label>Location *</label>
              <input
                type="text"
                name="location"
                value={formData.location}
                onChange={handleChange}
                className={errors.location ? 'error' : ''}
                placeholder="e.g. Bangkok"
              />
              {errors.location && <span className="error-text">{errors.location}</span>}
            </div>
          </div>

          <div className="form-group">
            <label>Description *</label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={errors.description ? 'error' : ''}
              rows="4"
              placeholder="Describe the car details..."
            />
            {errors.description && <span className="error-text">{errors.description}</span>}
          </div>
        </div>

        <div className="form-section">
          <h3>Car Images</h3>
          <div className="image-inputs">
            {formData.images.map((image, index) => (
              <div key={index} className="image-input-group">
                <input
                  type="url"
                  value={image}
                  onChange={(e) => handleImageChange(index, e.target.value)}
                  placeholder="Image URL"
                />
                {formData.images.length > 1 && (
                  <button
                    type="button"
                    className="remove-image-btn"
                    onClick={() => removeImageField(index)}
                  >
                    <Minus size={16} />
                  </button>
                )}
              </div>
            ))}
            <button
              type="button"
              className="add-image-btn"
              onClick={addImageField}
            >
              <Plus size={16} />
              Add Image
            </button>
          </div>
        </div>

        <div className="form-section">
          <h3>Seller Information</h3>
          
          <div className="form-row">
            <div className="form-group">
              <label>Seller Name *</label>
              <input
                type="text"
                name="seller.name"
                value={formData.seller.name}
                onChange={handleChange}
                className={errors['seller.name'] ? 'error' : ''}
                placeholder="Full name"
              />
              {errors['seller.name'] && <span className="error-text">{errors['seller.name']}</span>}
            </div>

            <div className="form-group">
              <label>Phone Number *</label>
              <input
                type="tel"
                name="seller.phone"
                value={formData.seller.phone}
                onChange={handleChange}
                className={errors['seller.phone'] ? 'error' : ''}
                placeholder="08X-XXX-XXXX"
              />
              {errors['seller.phone'] && <span className="error-text">{errors['seller.phone']}</span>}
            </div>
          </div>

          <div className="form-group">
            <label>Email</label>
            <input
              type="email"
              name="seller.email"
              value={formData.seller.email}
              onChange={handleChange}
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div className="form-actions">
          <button type="button" className="cancel-btn" onClick={onCancel}>
            Cancel
          </button>
          <button type="submit" className="save-btn" disabled={loading}>
            {loading ? 'Saving...' : (car ? 'Update' : 'Submit')}
          </button>
        </div>
      </form>
    </div>
  )
}

export default CarForm
