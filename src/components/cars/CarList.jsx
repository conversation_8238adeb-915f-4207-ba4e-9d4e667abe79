import { useState } from 'react'
import { useCarContext } from '../../context/CarContext'
import CarCard from './CarCard'
import CarForm from './CarForm'
import CarDetail from './CarDetail'
import FilterPanel from './FilterPanel'
import { Grid, List, Filter } from 'lucide-react'
import './CarList.css'

const CarList = ({ showAddForm, onCloseAddForm, onAddCar }) => {
  const { cars, loading, addCar, updateCar, deleteCar } = useCarContext()
  const [viewMode, setViewMode] = useState('grid')
  const [showForm, setShowForm] = useState(false)
  const [showDetail, setShowDetail] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [selectedCar, setSelectedCar] = useState(null)
  const [formLoading, setFormLoading] = useState(false)

  const handleAddCar = () => {
    setSelectedCar(null)
    setShowForm(true)
  }

  const handleEditCar = (car) => {
    setSelectedCar(car)
    setShowForm(true)
  }

  const handleViewCar = (car) => {
    setSelectedCar(car)
    setShowDetail(true)
  }

  const handleSaveCar = async (carData) => {
    setFormLoading(true)
    try {
      let result
      if (selectedCar) {
        result = await updateCar(selectedCar.id, carData)
      } else {
        result = await addCar(carData)
      }
      
      if (result.success) {
        setShowForm(false)
        setSelectedCar(null)
        // If this was triggered from external add car button
        if (onCloseAddForm) {
          onCloseAddForm()
        }
      }
    } catch (error) {
      console.error('Error saving car:', error)
    } finally {
      setFormLoading(false)
    }
  }

  const handleDeleteCar = async (carId) => {
    const result = await deleteCar(carId)
    if (!result.success) {
      alert('เกิดข้อผิดพลาดในการลบรถ')
    }
  }

  const handleCloseForm = () => {
    setShowForm(false)
    setSelectedCar(null)
    if (onCloseAddForm) {
      onCloseAddForm()
    }
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading...</p>
      </div>
    )
  }

  return (
    <div className="car-list-container">
      <div className="car-list-header">
        <div className="header-left">
          <h1>AllCar ({cars.length})</h1>
        </div>
        
        <div className="header-controls">
          <button
            className={`filter-btn ${showFilters ? 'active' : ''}`}
            onClick={() => setShowFilters(!showFilters)}
          >
            <Filter size={20} />
            Filter
          </button>
          
          <div className="view-controls">
            <button
              className={`view-btn ${viewMode === 'grid' ? 'active' : ''}`}
              onClick={() => setViewMode('grid')}
            >
              <Grid size={20} />
            </button>
            <button
              className={`view-btn ${viewMode === 'list' ? 'active' : ''}`}
              onClick={() => setViewMode('list')}
            >
              <List size={20} />
            </button>
          </div>
          
          <button className="add-car-btn" onClick={handleAddCar}>
            Add Car
          </button>
        </div>
      </div>

      <div className="car-list-content">
        {showFilters && (
          <div className="filters-panel">
            <FilterPanel />
          </div>
        )}
        
        <div className={`cars-container ${viewMode}`}>
          {cars.length === 0 ? (
            <div className="empty-state">
              <h3>ไม่พบรถที่ตรงกับเงื่อนไข</h3>
              <p>ลองปรับเปลี่ยนตัวกรองหรือค้นหาด้วยคำค้นอื่น</p>
              <button className="add-first-car-btn" onClick={handleAddCar}>
                ลงขายรถคันแรก
              </button>
            </div>
          ) : (
            cars.map(car => (
              <CarCard
                key={car.id}
                car={car}
                onEdit={handleEditCar}
                onDelete={handleDeleteCar}
                onView={handleViewCar}
              />
            ))
          )}
        </div>
      </div>

      {/* Form Modal - Internal */}
      {showForm && (
        <div className="modal-overlay" onClick={handleCloseForm}>
          <div onClick={(e) => e.stopPropagation()}>
            <CarForm
              car={selectedCar}
              onSave={handleSaveCar}
              onCancel={handleCloseForm}
              loading={formLoading}
            />
          </div>
        </div>
      )}

      {/* Form Modal - External (from Header button) */}
      {showAddForm && (
        <div className="modal-overlay" onClick={onCloseAddForm}>
          <div onClick={(e) => e.stopPropagation()}>
            <CarForm
              car={null}
              onSave={handleSaveCar}
              onCancel={onCloseAddForm}
              loading={formLoading}
            />
          </div>
        </div>
      )}

      {/* Detail Modal */}
      {showDetail && (
        <div className="modal-overlay" onClick={() => setShowDetail(false)}>
          <div onClick={(e) => e.stopPropagation()}>
            <CarDetail
              car={selectedCar}
              onClose={() => setShowDetail(false)}
              onEdit={handleEditCar}
            />
          </div>
        </div>
      )}
    </div>
  )
}

export default CarList