.filter-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 20px;
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.filter-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.clear-btn {
  background: none;
  border: none;
  color: #667eea;
  font-size: 14px;
  cursor: pointer;
  text-decoration: underline;
}

.clear-btn:hover {
  color: #5a6fd8;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.filter-group select,
.filter-group input {
  width: 100%;
  padding: 10px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: #667eea;
}

.price-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.price-range input {
  flex: 1;
}

.price-range span {
  color: #6b7280;
  font-size: 14px;
}

/* เพิ่มใน FilterPanel.css */
.price-examples {
  margin-top: 4px;
}

.price-examples small {
  color: #9ca3af;
  font-size: 11px;
}

.exchange-note {
  display: block;
  margin-top: 2px;
  color: #667eea;
  font-weight: 500;
}

.price-filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.currency-toggle-small {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 12px;
  font-weight: 500;
}

.currency-toggle-small:hover {
  background: #f3f4f6;
  border-color: #d1d5db;
}

.exchange-note {
  display: block;
  margin-top: 2px;
  color: #667eea;
  font-weight: 500;
}