.car-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.car-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.car-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.car-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.car-card:hover .car-image {
  transform: scale(1.05);
}

.car-condition {
  position: absolute;
  top: 12px;
  left: 12px;
}

.condition-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.condition-badge.new {
  background: #10b981;
}

.condition-badge.used {
  background: #f59e0b;
}

/* Interest Button - ปุ่มหัวใจ */
.interest-btn {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  color: #6b7280;
  z-index: 10;
}

.interest-btn:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 1);
}

.interest-btn.active {
  background: rgba(239, 68, 68, 0.9);
  color: white;
}

.interest-btn.active:hover {
  background: rgba(239, 68, 68, 1);
}

.interest-btn.animating {
  animation: heartPulse 0.3s ease-in-out;
}

@keyframes heartPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.3);
  }
  100% {
    transform: scale(1);
  }
}

.heart-icon {
  transition: all 0.2s ease;
}

.interest-btn.active .heart-icon {
  animation: heartBeat 0.6s ease-in-out;
}

@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Action Buttons - ปรับตำแหน่งให้ไม่ทับกับปุ่มหัวใจ */
.car-actions {
  position: absolute;
  top: 56px; /* เลื่อนลงมาข้างล่างปุ่มหัวใจ */
  right: 12px;
  display: flex;
  flex-direction: column; /* เรียงแนวตั้ง */
  gap: 8px;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.action-btn.view {
  background: rgba(59, 130, 246, 0.9);
  color: white;
}

.action-btn.edit {
  background: rgba(16, 185, 129, 0.9);
  color: white;
}

.action-btn.delete {
  background: rgba(239, 68, 68, 0.9);
  color: white;
}

.action-btn:hover {
  transform: scale(1.1);
}

/* เพิ่มเอฟเฟกต์เมื่อ hover card */
.car-card:hover .interest-btn {
  transform: scale(1.05);
}

.car-card:hover .interest-btn.active {
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.car-info {
  padding: 16px;
}

.car-title {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.car-price {
  font-size: 18px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 12px;
}

.car-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
}

.detail-item svg {
  width: 14px;
  height: 14px;
}

.car-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #e5e7eb;
  padding-top: 12px;
}

.seller-name {
  font-size: 13px;
  color: #374151;
  font-weight: 500;
}

.car-date {
  font-size: 12px;
  color: #9ca3af;
}

.car-price-container {
  position: relative;
}

.car-price {
  font-size: 18px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s;
}

.car-price:hover {
  color: #5a6fd8;
}

.price-toggle {
  transition: color 0.2s;
}

.price-toggle.usd {
  color: #10b981;
}

.price-toggle.khr {
  color: #f59e0b;
}

.original-price-note {
  font-size: 12px;
  color: #9ca3af;
  margin-top: -8px;
  margin-bottom: 8px;
  font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .car-card {
    margin-bottom: 16px;
  }
  
  .car-details {
    flex-direction: column;
  }
  
  .car-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .car-actions {
    flex-direction: row;
    top: 12px;
    right: 56px; /* ให้เว้นพื้นที่กับปุ่มหัวใจ */
  }
}

/* เพิ่มเอฟเฟกต์สำหรับโหมดมืด (ถ้าต้องการ) */
@media (prefers-color-scheme: dark) {
  .interest-btn {
    background: rgba(31, 41, 55, 0.9);
    color: #9ca3af;
  }
  
  .interest-btn:hover {
    background: rgba(31, 41, 55, 1);
  }
}