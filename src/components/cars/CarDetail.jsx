import { useState } from 'react'
import { X, Edit, MapPin, Calendar, Gauge, Phone, Mail, DollarSign, Info, ToggleLeft, ToggleRight } from 'lucide-react'
import { formatPriceWithBoth, formatDate, getCurrentExchangeRate } from '../../utils/formatters'
import './CarDetail.css'

const CarDetail = ({ car, onClose, onEdit }) => {
  const [showKhrPrice, setShowKhrPrice] = useState(false) // เริ่มต้นด้วย USD

  if (!car) return null

  const priceInfo = formatPriceWithBoth(car.price)
  const exchangeRate = getCurrentExchangeRate()

  return (
    <div className="car-detail-container">
      <div className="car-detail-header">
        <h2>{car.title}</h2>
        <div className="header-actions">
          <button className="edit-btn" onClick={() => onEdit(car)}>
            <Edit size={20} />
            Edit
          </button>
          <button className="close-btn" onClick={onClose}>
            <X size={24} />
          </button>
        </div>
      </div>

      <div className="car-detail-content">
        <div className="car-images">
          <div className="main-image">
            <img src={car.images[0]} alt={car.title} />
          </div>
          {car.images.length > 1 && (
            <div className="image-thumbnails">
              {car.images.map((image, index) => (
                <img key={index} src={image} alt={`${car.title} ${index + 1}`} />
              ))}
            </div>
          )}
        </div>

        <div className="car-info">
          <div className="price-section">
            <div className="price-main">
              <div className="price-header">
                <div className={`price-display ${showKhrPrice ? 'khr' : 'usd'}`}>
                  {showKhrPrice ? priceInfo.khr : priceInfo.usd}
                </div>
                <button 
                  className="currency-toggle"
                  onClick={() => setShowKhrPrice(!showKhrPrice)}
                  title={showKhrPrice ? "Switch to USD" : "Switch to Cambodian Riel"}
                >
                  {showKhrPrice ? <ToggleRight size={24} /> : <ToggleLeft size={24} />}
                  <span className="currency-label">
                    {showKhrPrice ? 'KHR' : 'USD'}
                  </span>
                </button>
              </div>
              
              <div className="price-detail">
                <div className="alternative-price">
                  <DollarSign size={16} />
                </div>
                <div className="exchange-rate">
                  <Info size={14} />
                  <span>Exchange Rate: 1 USD = {exchangeRate.toLocaleString()} KHR</span>
                </div>
              </div>
            </div>
            <div className={`condition ${car.condition}`}>
              {car.condition === 'new' ? 'New' : 'Used'}
            </div>
          </div>

          <div className="basic-info">
            <div className="info-item">
              <Calendar size={16} />
              <span>Year {car.year}</span>
            </div>
            <div className="info-item">
              <Gauge size={16} />
              <span>{car.mileage.toLocaleString()} km</span>
            </div>
            <div className="info-item">
              <MapPin size={16} />
              <span>{car.location}</span>
            </div>
          </div>

          <div className="specifications">
            <h3>Specifications</h3>
            <div className="spec-grid">
              <div className="spec-item">
                <span className="spec-label">Brand:</span>
                <span className="spec-value">{car.brand}</span>
              </div>
              <div className="spec-item">
                <span className="spec-label">Model:</span>
                <span className="spec-value">{car.model}</span>
              </div>
              <div className="spec-item">
                <span className="spec-label">Color:</span>
                <span className="spec-value">{car.color}</span>
              </div>
              <div className="spec-item">
                <span className="spec-label">Fuel:</span>
                <span className="spec-value">{car.fuel}</span>
              </div>
              <div className="spec-item">
                <span className="spec-label">Transmission:</span>
                <span className="spec-value">{car.transmission}</span>
              </div>
              <div className="spec-item">
                <span className="spec-label">Engine:</span>
                <span className="spec-value">{car.carCC} CC</span>
              </div>
            </div>
          </div>

          <div className="description">
            <h3>Description</h3>
            <p>{car.description}</p>
          </div>

          <div className="seller-info">
            <h3>Seller Information</h3>
            <div className="seller-details">
              <div className="seller-name">{car.seller.name}</div>
              <div className="contact-info">
                <div className="contact-item">
                  <Phone size={16} />
                  <span>{car.seller.phone}</span>
                </div>
                {car.seller.email && (
                  <div className="contact-item">
                    <Mail size={16} />
                    <span>{car.seller.email}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="listing-info">
            <div className="listing-date">
              Listed: {formatDate(car.createdAt)}
            </div>
            {car.updatedAt !== car.createdAt && (
              <div className="update-date">
                Last updated: {formatDate(car.updatedAt)}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CarDetail