// components/CarCard/CarCard.js
import { useState } from 'react'
import { Edit, Trash2, Eye, MapPin, Calendar, Gauge, DollarSign, Heart } from 'lucide-react'
import { formatPriceWithBoth, formatDate } from '../../utils/formatters'
import { useInterest } from '../../context/InterestContext'
import './CarCard.css'

const CarCard = ({ car, onEdit, onDelete, onView }) => {
  const [showKhrPrice, setShowKhrPrice] = useState(false)
  const [showActions, setShowActions] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  
  const { toggleInterest, isCarInterested } = useInterest()
  const priceInfo = formatPriceWithBoth(car.price)
  const isInterested = isCarInterested(car.id)

  console.log(`Car ${car.id} is interested:`, isInterested) // Debug

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this car?')) {
      onDelete(car.id)
    }
  }

  const handleToggleInterest = (e) => {
    e.stopPropagation()
    e.preventDefault()
    
    console.log('Toggle interest clicked for car:', car.id) // Debug
    
    setIsAnimating(true)
    
    const newInterestState = toggleInterest(car)
    console.log('New interest state:', newInterestState) // Debug
    
    // แสดงแจ้งเตือนแบบง่าย ๆ (ในการใช้งานจริงอาจใช้ toast library)
    const message = newInterestState 
      ? `เพิ่ม "${car.title}" ในรายการที่สนใจแล้ว` 
      : `ลบ "${car.title}" ออกจากรายการที่สนใจแล้ว`
    
    // แสดงแจ้งเตือนด้วย alert ชั่วคราว (ควรเปลี่ยนเป็น toast ในการใช้งานจริง)
    setTimeout(() => {
      alert(message)
    }, 100)
    
    setTimeout(() => {
      setIsAnimating(false)
    }, 300)
  }

  return (
    <div 
      className="car-card"
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className="car-image-container">
        <img
          src={car.images[0]}
          alt={car.title}
          className="car-image"
        />
        <div className="car-condition">
          <span className={`condition-badge ${car.condition}`}>
            {car.condition === 'new' ? 'New' : 'Used'}
          </span>
        </div>

        <button 
          className={`interest-btn ${isInterested ? 'active' : ''} ${isAnimating ? 'animating' : ''}`}
          onClick={handleToggleInterest}
          title={isInterested ? 'ลบออกจากรายการสนใจ' : 'เพิ่มในรายการสนใจ'}
        >
          <Heart 
            size={16} 
            fill={isInterested ? 'currentColor' : 'none'}
            className="heart-icon"
          />
        </button>
        
        {showActions && (
          <div className="car-actions">
            <button className="action-btn view" onClick={() => onView(car)}>
              <Eye size={16} />
            </button>
            <button className="action-btn edit" onClick={() => onEdit(car)}>
              <Edit size={16} />
            </button>
            <button className="action-btn delete" onClick={handleDelete}>
              <Trash2 size={16} />
            </button>
          </div>
        )}
      </div>
      
      <div className="car-info">
        <h3 className="car-title">{car.title}</h3>
        
        <div className="car-price-container">
          <div 
            className="car-price" 
            onClick={() => setShowKhrPrice(!showKhrPrice)}
            title={showKhrPrice ? "Click to show USD price" : "Click to show Riel price"}
          >
            {showKhrPrice ? priceInfo.khr : priceInfo.usd}
            <DollarSign 
              size={16} 
              className={`price-toggle ${showKhrPrice ? 'khr' : 'usd'}`}
            />
          </div>
        </div>
        
        <div className="car-details">
          <div className="detail-item">
            <Calendar size={14} />
            <span>{car.year}</span>
          </div>
          <div className="detail-item">
            <Gauge size={14} />
            <span>{car.mileage.toLocaleString()} km</span>
          </div>
          <div className="detail-item">
            <MapPin size={14} />
            <span>{car.location}</span>
          </div>
        </div>
        
        <div className="car-meta">
          <div className="seller-info">
            <span className="seller-name">{car.seller.name}</span>
          </div>
          <div className="car-date">
            {formatDate(car.createdAt)}
          </div>
        </div>
      </div>
    </div>
  )
}

export default CarCard