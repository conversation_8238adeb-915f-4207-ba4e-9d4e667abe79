.car-detail-container {
  background: white;
  border-radius: 12px;
  max-width: 900px;
  width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.car-detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
  border-radius: 12px 12px 0 0;
}

.car-detail-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.edit-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  font-weight: 500;
}

.edit-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.close-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 6px;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #e5e7eb;
}

.car-detail-content {
  padding: 24px;
}

.car-images {
  margin-bottom: 24px;
}

.main-image {
  width: 100%;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  position: relative;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.main-image:hover img {
  transform: scale(1.02);
}

.image-thumbnails {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.image-thumbnails::-webkit-scrollbar {
  height: 6px;
}

.image-thumbnails::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.image-thumbnails::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.image-thumbnails::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.image-thumbnails img {
  width: 80px;
  height: 60px;
  border-radius: 4px;
  object-fit: cover;
  cursor: pointer;
  transition: all 0.2s;
  border: 2px solid transparent;
}

.image-thumbnails img:hover {
  transform: scale(1.05);
  border-color: #667eea;
}

.price-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.price {
  font-size: 32px;
  font-weight: 700;
  color: #667eea;
  text-shadow: 0 2px 4px rgba(102, 126, 234, 0.1);
}

.condition {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.condition.new {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.condition.used {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);
}

.basic-info {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  padding: 20px;
  background: #f9fafb;
  border-radius: 12px;
  border: 1px solid #e5e7eb;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.info-item svg {
  color: #667eea;
}

.specifications {
  margin-bottom: 24px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
}

.specifications h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #667eea;
}

.spec-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s;
}

.spec-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.spec-label {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

.spec-value {
  color: #1a1a1a;
  font-size: 14px;
  font-weight: 600;
}

.description {
  margin-bottom: 24px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 20px;
}

.description h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #667eea;
}

.description p {
  color: #6b7280;
  line-height: 1.7;
  font-size: 15px;
  margin: 0;
}

.seller-info {
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid #bae6fd;
}

.seller-info h3 {
  font-size: 18px;
  font-weight: 600;
  color: #0c4a6e;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.seller-info h3::before {
  content: "👤";
  font-size: 20px;
}

.seller-details {
  background: rgba(255, 255, 255, 0.8);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid rgba(186, 230, 253, 0.5);
}

.seller-name {
  font-size: 16px;
  font-weight: 600;
  color: #0c4a6e;
  margin-bottom: 12px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #0369a1;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 6px;
  transition: all 0.2s;
}

.contact-item:hover {
  background: rgba(255, 255, 255, 0.9);
  transform: translateX(4px);
}

.contact-item svg {
  color: #0284c7;
}

.listing-info {
  text-align: center;
  color: #9ca3af;
  font-size: 13px;
  border-top: 1px solid #e5e7eb;
  padding-top: 16px;
  background: #f9fafb;
  margin: -24px -24px 0 -24px;
  padding: 16px 24px 24px 24px;
  border-radius: 0 0 12px 12px;
}

.listing-date {
  margin-bottom: 4px;
}

.update-date {
  color: #6b7280;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .car-detail-container {
    width: 95vw;
    max-height: 95vh;
  }
  
  .car-detail-header {
    padding: 16px;
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .car-detail-content {
    padding: 16px;
  }
  
  .main-image {
    height: 250px;
  }
  
  .price-section {
    flex-direction: column;
    gap: 12px;
    text-align: center;
    padding: 16px;
  }
  
  .price {
    font-size: 28px;
  }
  
  .basic-info {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }
  
  .spec-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .spec-item {
    padding: 10px 12px;
  }
  
  .contact-info {
    gap: 6px;
  }
  
  .contact-item {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .listing-info {
    padding: 12px 16px 16px 16px;
    margin: -16px -16px 0 -16px;
  }
}

@media (max-width: 480px) {
  .car-detail-container {
    width: 98vw;
    max-height: 98vh;
  }
  
  .car-detail-header h2 {
    font-size: 20px;
  }
  
  .main-image {
    height: 200px;
  }
  
  .price {
    font-size: 24px;
  }
  
  .image-thumbnails img {
    width: 60px;
    height: 45px;
  }
  
  .basic-info {
    padding: 12px;
  }
  
  .specifications,
  .description,
  .seller-info {
    padding: 16px;
  }
  
  .specifications h3,
  .description h3,
  .seller-info h3 {
    font-size: 16px;
  }
}

/* Loading Animation */
.car-detail-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  flex-direction: column;
  gap: 16px;
}

.car-detail-loading .spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Smooth Animations */
.car-detail-container {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.spec-item,
.contact-item {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Print Styles */
@media print {
  .car-detail-header {
    position: static;
  }
  
  .header-actions {
    display: none;
  }
  
  .car-detail-container {
    max-height: none;
    overflow: visible;
    box-shadow: none;
  }
  
  .basic-info,
  .specifications,
  .description,
  .seller-info {
    break-inside: avoid;
  }
}

.price-main {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.price-thb {
  font-size: 32px;
  font-weight: 700;
  color: #667eea;
  text-shadow: 0 2px 4px rgba(102, 126, 234, 0.1);
}

.price-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.original-price,
.exchange-rate {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
}

.original-price {
  font-weight: 500;
}

.exchange-rate {
  font-size: 12px;
  color: #9ca3af;
}

.price-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.price-display {
  font-size: 32px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(102, 126, 234, 0.1);
}

.price-display.usd {
  color: #10b981;
}

.price-display.khr {
  color: #f59e0b;
}

.currency-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
  font-weight: 500;
}

.currency-toggle:hover {
  background: #e5e7eb;
  border-color: #d1d5db;
}

.currency-label {
  color: #374151;
}

.alternative-price,
.exchange-rate {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #6b7280;
}

.alternative-price {
  font-weight: 500;
}

.exchange-rate {
  font-size: 12px;
  color: #9ca3af;
}