import { useState } from 'react'
import Header from './common/Header'
import CarList from './cars/CarList'
import './Dashboard.css'

const Dashboard = () => {
  const [showAddForm, setShowAddForm] = useState(false)

  const handleAddCar = () => {
    setShowAddForm(true)
  }

  const handleCloseAddForm = () => {
    setShowAddForm(false)
  }

  return (
    <div className="dashboard">
      <main className="dashboard-main">
        <CarList 
          showAddForm={showAddForm}
          onCloseAddForm={handleCloseAddForm}
          onAddCar={handleAddCar}
        />
      </main>
    </div>
  )
}

export default Dashboard