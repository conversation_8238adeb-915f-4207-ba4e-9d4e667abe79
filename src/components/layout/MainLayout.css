.main-layout {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.main-content {
    flex: 1;
    padding-top: 70px; /* เว้นที่สำหรับ header ที่ fixed */
}

/* Modal overlay styles */
.main-layout .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
    animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.main-layout .modal-overlay > div {
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Dark mode */
.dark .main-layout {
    background: var(--bg-primary);
}

.dark .main-content {
    background: var(--bg-primary);
}

@media (max-width: 768px) {
    .main-content {
        padding-top: 60px;
    }
}