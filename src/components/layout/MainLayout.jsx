import { useState } from 'react'
import { Outlet } from 'react-router-dom'
import Header from '../common/Header'
import CarForm from '../cars/CarForm'
import { useCarContext } from '../../context/CarContext'
import './MainLayout.css'

const MainLayout = () => {
    const [showAddForm, setShowAddForm] = useState(false)
    const [formLoading, setFormLoading] = useState(false)
    const { addCar } = useCarContext()

    const handleAddCar = () => {
        setShowAddForm(true)
    }

    const handleCloseForm = () => {
        setShowAddForm(false)
    }

    const handleSaveCar = async (carData) => {
        setFormLoading(true)
        try {
            const result = await addCar(carData)
            
            if (result.success) {
                setShowAddForm(false)
                // อาจจะเพิ่ม notification หรือ toast message
                console.log('Car added successfully!')
            } else {
                // จัดการ error
                console.error('Failed to add car:', result.error)
            }
        } catch (error) {
            console.error('Error saving car:', error)
        } finally {
            setFormLoading(false)
        }
    }

    return (
        <div className="main-layout">
            <Header onAddCar={handleAddCar} />
            <main className="main-content">
                <Outlet />
            </main>

            {/* Car Form Modal */}
            {showAddForm && (
                <div className="modal-overlay" onClick={handleCloseForm}>
                    <div onClick={(e) => e.stopPropagation()}>
                        <CarForm
                            car={null}
                            onSave={handleSaveCar}
                            onCancel={handleCloseForm}
                            loading={formLoading}
                        />
                    </div>
                </div>
            )}
        </div>
    )
}

export default MainLayout