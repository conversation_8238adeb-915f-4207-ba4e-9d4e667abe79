// components/EntryManager/EntryManager.js
import React, { useState, useEffect } from 'react'
import { Plus, Edit2, Trash2, Search, Save, X, Tag } from 'lucide-react'
import './EntryManager.css'

const EntryManager = ({ 
  title, 
  type, 
  fields = [], 
  onSave,
  onDelete,
  data = [],
  icon
}) => {
  const [items, setItems] = useState(data)
  const [isEditing, setIsEditing] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [formData, setFormData] = useState({})

  useEffect(() => {
    setItems(data)
  }, [data])

  const handleAdd = () => {
    setIsEditing(true)
    setEditingItem(null)
    const initialData = {}
    fields.forEach(field => {
      initialData[field.key] = field.defaultValue || ''
    })
    setFormData(initialData)
  }

  const handleEdit = (item) => {
    setIsEditing(true)
    setEditingItem(item)
    setFormData({ ...item })
  }

  const handleSave = async () => {
    try {
      if (editingItem) {
        const updatedItems = items.map(item => 
          item.id === editingItem.id ? { ...formData, id: editingItem.id } : item
        )
        setItems(updatedItems)
      } else {
        const newItem = { ...formData, id: Date.now(), createdAt: new Date().toISOString() }
        setItems([...items, newItem])
      }
      
      if (onSave) {
        await onSave(formData, editingItem)
      }
      
      setIsEditing(false)
      setEditingItem(null)
      setFormData({})
    } catch (error) {
      console.error('Error saving item:', error)
      alert('Error saving item')
    }
  }

  const handleDelete = async (item) => {
    const displayName = item.name || item.value || item.title || 'this item'
    if (window.confirm(`Are you sure you want to delete "${displayName}"?`)) {
      try {
        const updatedItems = items.filter(i => i.id !== item.id)
        setItems(updatedItems)
        
        if (onDelete) {
          await onDelete(item)
        }
      } catch (error) {
        console.error('Error deleting item:', error)
        alert('Error deleting item')
      }
    }
  }

  const handleCancel = () => {
    setIsEditing(false)
    setEditingItem(null)
    setFormData({})
  }

  const filteredItems = items.filter(item => {
    const searchFields = ['name', 'value', 'title', 'description']
    return searchFields.some(field => {
      const fieldValue = item[field]
      return fieldValue && fieldValue.toString().toLowerCase().includes(searchTerm.toLowerCase())
    })
  })

  return (
    <div className="entry-manager">
      <div className="entry-header">
        <div className="entry-title">
          <div className="entry-icon">
            {icon || <Tag size={24} />}
          </div>
          <div>
            <h1>{title} Management</h1>
            <p>Manage {title.toLowerCase()} entries for your car listings</p>
          </div>
        </div>
        <button className="add-btn" onClick={handleAdd}>
          <Plus size={16} />
          Add {title}
        </button>
      </div>

      <div className="search-section">
        <div className="search-box">
          <Search size={16} />
          <input
            type="text"
            placeholder={`Search ${title.toLowerCase()}...`}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="stats">
          <span>{filteredItems.length} of {items.length} items</span>
        </div>
      </div>

      {isEditing && (
        <div className="edit-form-overlay">
          <div className="edit-form">
            <div className="form-header">
              <h3>{editingItem ? 'Edit' : 'Add'} {title}</h3>
              <button className="close-btn" onClick={handleCancel}>
                <X size={16} />
              </button>
            </div>
            <div className="form-body">
              {fields.map(field => (
                <div key={field.key} className="form-group">
                  <label>
                    {field.label}
                    {field.required && <span className="required">*</span>}
                  </label>
                  {field.type === 'select' ? (
                    <select
                      value={formData[field.key] || ''}
                      onChange={(e) => setFormData({...formData, [field.key]: e.target.value})}
                      required={field.required}
                    >
                      <option value="">Select {field.label}</option>
                      {field.options?.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  ) : field.type === 'textarea' ? (
                    <textarea
                      value={formData[field.key] || ''}
                      onChange={(e) => setFormData({...formData, [field.key]: e.target.value})}
                      placeholder={field.placeholder}
                      required={field.required}
                      rows={field.rows || 3}
                    />
                  ) : field.type === 'number' ? (
                    <input
                      type="number"
                      value={formData[field.key] || ''}
                      onChange={(e) => setFormData({...formData, [field.key]: e.target.value})}
                      placeholder={field.placeholder}
                      required={field.required}
                      min={field.min}
                      max={field.max}
                      step={field.step}
                    />
                  ) : field.type === 'color' ? (
                    <div className="color-input-group">
                      <input
                        type="color"
                        value={formData[field.key] || '#000000'}
                        onChange={(e) => setFormData({...formData, [field.key]: e.target.value})}
                        className="color-picker"
                      />
                      <input
                        type="text"
                        value={formData[field.key] || ''}
                        onChange={(e) => setFormData({...formData, [field.key]: e.target.value})}
                        placeholder={field.placeholder}
                        className="color-text"
                      />
                    </div>
                  ) : (
                    <input
                      type={field.type || 'text'}
                      value={formData[field.key] || ''}
                      onChange={(e) => setFormData({...formData, [field.key]: e.target.value})}
                      placeholder={field.placeholder}
                      required={field.required}
                    />
                  )}
                  {field.description && (
                    <small className="field-description">{field.description}</small>
                  )}
                </div>
              ))}
            </div>
            <div className="form-actions">
              <button className="cancel-btn" onClick={handleCancel}>
                Cancel
              </button>
              <button className="save-btn" onClick={handleSave}>
                <Save size={16} />
                {editingItem ? 'Update' : 'Save'}
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="items-list">
        {filteredItems.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">
              {icon || <Tag size={48} />}
            </div>
            <h3>No {title.toLowerCase()} found</h3>
            <p>{searchTerm ? 'Try adjusting your search term' : `Start by adding your first ${title.toLowerCase()}`}</p>
            {!searchTerm && (
              <button className="empty-add-btn" onClick={handleAdd}>
                <Plus size={16} />
                Add {title}
              </button>
            )}
          </div>
        ) : (
          <div className="items-grid">
            {filteredItems.map(item => (
              <div key={item.id} className="item-card">
                <div className="item-content">
                  <div className="item-main">
                    <div className="item-header">
                      <h4>{item.name || item.value || item.title}</h4>
                      {item.colorCode && (
                        <div 
                          className="color-preview" 
                          style={{ backgroundColor: item.colorCode }}
                          title={item.colorCode}
                        ></div>
                      )}
                    </div>
                    {item.description && (
                      <p className="item-description">{item.description}</p>
                    )}
                    <div className="item-meta">
                      {item.country && <span className="meta-tag">📍 {item.country}</span>}
                      {item.category && <span className="meta-tag">🏷️ {item.category}</span>}
                      {item.minValue && item.maxValue && (
                        <span className="meta-tag">⚡ {item.minValue}-{item.maxValue}</span>
                      )}
                      {item.value && typeof item.value === 'number' && (
                        <span className="meta-tag">🔢 {item.value}</span>
                      )}
                    </div>
                  </div>
                  <div className="item-actions">
                    <button 
                      className="edit-btn"
                      onClick={() => handleEdit(item)}
                      title="Edit"
                    >
                      <Edit2 size={14} />
                    </button>
                    <button 
                      className="delete-btn"
                      onClick={() => handleDelete(item)}
                      title="Delete"
                    >
                      <Trash2 size={14} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default EntryManager