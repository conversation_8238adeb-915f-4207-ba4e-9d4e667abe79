/* EntryManager.css */
.entry-manager {
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
    background: #f8fafc;
    min-height: 100vh;
}

.entry-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
    background: white;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.entry-title {
    display: flex;
    align-items: flex-start;
    gap: 16px;
}

.entry-icon {
    background: #1C466E;
    color: white;
    padding: 12px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.entry-title h1 {
    margin: 0 0 4px 0;
    color: #1a202c;
    font-size: 28px;
    font-weight: 700;
}

.entry-title p {
    margin: 0;
    color: #718096;
    font-size: 16px;
}

.add-btn {
    background: linear-gradient(135deg, #1C466E 0%, #2a5885 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 500;
    font-size: 14px;
}

.add-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(28, 70, 110, 0.3);
}

.search-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.search-box {
    position: relative;
    flex: 1;
    max-width: 400px;
}

.search-box svg {
    position: absolute;
    left: 14px;
    top: 50%;
    transform: translateY(-50%);
    color: #000000;
}

.search-box input {
    width: 100%;
    padding: 12px 12px 12px 44px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s;
}

.search-box input:focus {
    outline: none;
    border-color: #1C466E;
    box-shadow: 0 0 0 3px rgba(28, 70, 110, 0.1);
}

.stats {
    color: #000000;
    font-size: 14px;
    font-weight: 500;
}

.edit-form-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.edit-form {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.form-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: #f7fafc;
    border-bottom: 1px solid #e2e8f0;
}

.form-header h3 {
    margin: 0;
    color: #1a202c;
    font-size: 20px;
    font-weight: 600;
}

.close-btn {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #718096;
    transition: all 0.2s;
}

.close-btn:hover {
    background: #edf2f7;
    color: #4a5568;
}

.form-body {
    padding: 24px;
    max-height: 60vh;
    overflow-y: auto;
    display: grid;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 600;
    color: #2d3748;
    font-size: 14px;
}

.required {
    color: #e53e3e;
    margin-left: 4px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 12px;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.2s;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1C466E;
    box-shadow: 0 0 0 3px rgba(28, 70, 110, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.color-input-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.color-picker {
    width: 50px;
    height: 40px;
    padding: 0;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
}

.color-text {
    flex: 1;
}

.field-description {
    color: #718096;
    font-size: 12px;
    font-style: italic;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 20px 24px;
    background: #f7fafc;
    border-top: 1px solid #e2e8f0;
}

.cancel-btn {
    background: #f7fafc;
    color: #4a5568;
    border: 1px solid #e2e8f0;
    padding: 10px 20px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.cancel-btn:hover {
    background: #edf2f7;
    border-color: #cbd5e0;
}

.save-btn {
    background: linear-gradient(135deg, #1C466E 0%, #2a5885 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.save-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(28, 70, 110, 0.3);
}

.items-list {
    background: white;
    border-radius: 12px;
    padding: 24px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #718096;
}

.empty-icon {
    margin-bottom: 20px;
    color: #cbd5e0;
}

.empty-state h3 {
    margin: 0 0 8px 0;
    color: #4a5568;
    font-size: 20px;
    font-weight: 600;
}

.empty-state p {
    margin: 0 0 24px 0;
    font-size: 16px;
}

.empty-add-btn {
    background: linear-gradient(135deg, #1C466E 0%, #2a5885 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.2s;
}

.empty-add-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(28, 70, 110, 0.3);
}

.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
}

.item-card {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.2s;
    position: relative;
}

.item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #cbd5e0;
}

.item-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.item-main {
    flex: 1;
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.item-header h4 {
    margin: 0;
    color: #1a202c;
    font-size: 18px;
    font-weight: 600;
}

.color-preview {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    border: 2px solid #e2e8f0;
    flex-shrink: 0;
}

.item-description {
    margin: 0 0 12px 0;
    color: #718096;
    font-size: 14px;
    line-height: 1.5;
}

.item-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 16px;
}

.meta-tag {
    background: #edf2f7;
    color: #4a5568;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.item-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-left: 16px;
}

.edit-btn,
.delete-btn {
    width: 36px;
    height: 36px;
    border: none;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 14px;
}

.edit-btn {
    background: #aedbf3;
    color: #2b6cb0;
    border: 1px solid #bee3f8;
}

.edit-btn:hover {
    background: #bee3f8;
    border-color: #90cdf4;
    transform: scale(1.05);
}

.delete-btn {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
}

.delete-btn:hover {
    background: #feb2b2;
    border-color: #fc8181;
    transform: scale(1.05);
}

/* Responsive Design */
@media (max-width: 768px) {
    .entry-manager {
        padding: 16px;
    }

    .entry-header {
        flex-direction: column;
        gap: 20px;
        align-items: stretch;
    }

    .search-section {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
    }

    .search-box {
        max-width: none;
    }

    .items-grid {
        grid-template-columns: 1fr;
    }

    .edit-form-overlay {
        padding: 10px;
    }

    .form-body {
        max-height: 50vh;
    }

    .item-actions {
        flex-direction: row;
        margin-left: 0;
        margin-top: 12px;
    }

    .item-content {
        flex-direction: column;
    }
}

@media (max-width: 480px) {
    .entry-title {
        flex-direction: column;
        gap: 12px;
    }

    .entry-title h1 {
        font-size: 24px;
    }

    .form-actions {
        flex-direction: column;
    }

    .cancel-btn,
    .save-btn {
        width: 100%;
        justify-content: center;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .entry-manager {
        /* background: #1a202c; */
        background: transparent;
        /* หรือใช้ transparent แทน */
    }

    .entry-header,
    .search-section,
    .items-list,
    .edit-form {
        background: #a4bce3;
        color: #e2e8f0;
    }

    .item-card {
        background: #e2e8f0;
        border-color: #718096;
    }

    /* Light theme styles */
    .form-header {
        background: white;
        border-color: #e2e8f0;
        color: #1a202c;
    }

    .form-header h3 {
        color: #1a202c;
    }

    .form-actions {
        background: white;
        border-color: #e2e8f0;
    }

    .search-box input,
    .form-group input,
    .form-group select,
    .form-group textarea {
        background: white;
        border-color: #e2e8f0;
        color: #1a202c;
    }

    .search-box input:focus,
    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        border-color: #1C466E;
        box-shadow: 0 0 0 3px rgba(28, 70, 110, 0.1);
    }

    .search-box input::placeholder,
    .form-group input::placeholder,
    .form-group textarea::placeholder {
        color: #a0aec0;
    }

    .form-group label {
        color: #2d3748;
    }

    .field-description {
        color: #718096;
    }

    .meta-tag {
        background: #f7fafc;
        color: #4a5568;
        border: 1px solid #e2e8f0;
    }

    .close-btn {
        background: #f7fafc;
        border-color: #e2e8f0;
        color: #718096;
    }

    .close-btn:hover {
        background: #edf2f7;
        color: #4a5568;
    }
}