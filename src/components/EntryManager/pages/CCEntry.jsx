// pages/Entries/CCEntry.js
import React, { useState, useEffect } from 'react'
import { Gauge } from 'lucide-react'
import EntryManager from '../EntryManager'

const CCEntry = () => {
  const [ccSizes, setCCSizes] = useState([])

  useEffect(() => {
    loadCCSizes()
  }, [])

  const loadCCSizes = () => {
    const savedCCSizes = localStorage.getItem('car_cc_sizes')
    if (savedCCSizes) {
      setCCSizes(JSON.parse(savedCCSizes))
    } else {
      const mockCCSizes = [
        { 
          id: 1, 
          name: '800cc', 
          value: 800,
          category: 'Small',
          description: 'Small displacement for city cars',
          fuelEfficiency: 'Excellent'
        },
        { 
          id: 2, 
          name: '1000cc', 
          value: 1000,
          category: 'Small',
          description: 'Compact engine for small cars',
          fuelEfficiency: 'Very Good'
        },
        { 
          id: 3, 
          name: '1200cc', 
          value: 1200,
          category: 'Small',
          description: 'Common size for compact cars',
          fuelEfficiency: 'Good'
        },
        { 
          id: 4, 
          name: '1500cc', 
          value: 1500,
          category: 'Medium',
          description: 'Mid-size engine for family cars',
          fuelEfficiency: 'Good'
        },
        { 
          id: 5, 
          name: '1800cc', 
          value: 1800,
          category: 'Medium',
          description: 'Popular size for sedans',
          fuelEfficiency: 'Fair'
        },
        { 
          id: 6, 
          name: '2000cc', 
          value: 2000,
          category: 'Large',
          description: 'Large engine for performance',
          fuelEfficiency: 'Fair'
        },
        { 
          id: 7, 
          name: '2500cc', 
          value: 2500,
          category: 'Large',
          description: 'High-performance engine',
          fuelEfficiency: 'Low'
        },
        { 
          id: 8, 
          name: '3000cc+', 
          value: 3000,
          category: 'Very Large',
          description: 'Luxury and sports cars',
          fuelEfficiency: 'Low'
        }
      ]
      setCCSizes(mockCCSizes)
      localStorage.setItem('car_cc_sizes', JSON.stringify(mockCCSizes))
    }
  }

  const handleSave = async (ccData, existingCC) => {
    let updatedCCSizes
    if (existingCC) {
      updatedCCSizes = ccSizes.map(cc => 
        cc.id === existingCC.id ? { ...ccData, id: existingCC.id } : cc
      )
    } else {
      const newCC = { ...ccData, id: Date.now() }
      updatedCCSizes = [...ccSizes, newCC]
    }
    setCCSizes(updatedCCSizes)
    localStorage.setItem('car_cc_sizes', JSON.stringify(updatedCCSizes))
  }

  const handleDelete = async (cc) => {
    const updatedCCSizes = ccSizes.filter(c => c.id !== cc.id)
    setCCSizes(updatedCCSizes)
    localStorage.setItem('car_cc_sizes', JSON.stringify(updatedCCSizes))
  }

  const fields = [
    {
      key: 'name',
      label: 'CC Name',
      type: 'text',
      placeholder: 'e.g., 1500cc',
      required: true
    },
    {
      key: 'value',
      label: 'Engine Size (CC)',
      type: 'number',
      placeholder: 'e.g., 1500',
      required: true,
      min: 50,
      max: 10000,
      step: 50
    },
    {
      key: 'category',
      label: 'Category',
      type: 'select',
      required: true,
      options: [
        { value: 'Very Small', label: 'Very Small (< 800cc)' },
        { value: 'Small', label: 'Small (800-1200cc)' },
        { value: 'Medium', label: 'Medium (1200-2000cc)' },
        { value: 'Large', label: 'Large (2000-3000cc)' },
        { value: 'Very Large', label: 'Very Large (> 3000cc)' }
      ]
    },
    {
      key: 'fuelEfficiency',
      label: 'Fuel Efficiency',
      type: 'select',
      required: true,
      options: [
        { value: 'Excellent', label: 'Excellent' },
        { value: 'Very Good', label: 'Very Good' },
        { value: 'Good', label: 'Good' },
        { value: 'Fair', label: 'Fair' },
        { value: 'Low', label: 'Low' }
      ]
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Enter engine size description',
      rows: 2
    }
  ]

  return (
    <EntryManager
      title="Engine Size (CC)"
      type="cc"
      fields={fields}
      data={ccSizes}
      onSave={handleSave}
      onDelete={handleDelete}
      icon={<Gauge size={24} />}
    />
  )
}

export default CCEntry