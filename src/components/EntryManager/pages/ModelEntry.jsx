// pages/Entries/ModelEntry.js
import React, { useState, useEffect } from 'react'
import { Car } from 'lucide-react'
import EntryManager from '../EntryManager'

const ModelEntry = () => {
  const [models, setModels] = useState([])
  const [brands, setBrands] = useState([])

  useEffect(() => {
    loadModels()
    loadBrands()
  }, [])

  const loadBrands = () => {
    const savedBrands = localStorage.getItem('car_brands')
    if (savedBrands) {
      setBrands(JSON.parse(savedBrands))
    }
  }

  const loadModels = () => {
    const savedModels = localStorage.getItem('car_models')
    if (savedModels) {
      setModels(JSON.parse(savedModels))
    } else {
      const mockModels = [
        { 
          id: 1, 
          name: 'Civic', 
          brand: 'Honda',
          description: 'Compact car known for reliability and fuel efficiency',
          segment: 'Compact'
        },
        { 
          id: 2, 
          name: '<PERSON><PERSON>', 
          brand: 'Toyota',
          description: 'Mid-size sedan with excellent reputation',
          segment: 'Mid-size'
        },
        { 
          id: 3, 
          name: '3 Series', 
          brand: 'BMW',
          description: 'Luxury compact executive car',
          segment: 'Luxury'
        }
      ]
      setModels(mockModels)
      localStorage.setItem('car_models', JSON.stringify(mockModels))
    }
  }

  const handleSave = async (modelData, existingModel) => {
    let updatedModels
    if (existingModel) {
      updatedModels = models.map(model => 
        model.id === existingModel.id ? { ...modelData, id: existingModel.id } : model
      )
    } else {
      const newModel = { ...modelData, id: Date.now() }
      updatedModels = [...models, newModel]
    }
    setModels(updatedModels)
    localStorage.setItem('car_models', JSON.stringify(updatedModels))
  }

  const handleDelete = async (model) => {
    const updatedModels = models.filter(m => m.id !== model.id)
    setModels(updatedModels)
    localStorage.setItem('car_models', JSON.stringify(updatedModels))
  }

  const fields = [
    {
      key: 'name',
      label: 'Model Name',
      type: 'text',
      placeholder: 'Enter model name',
      required: true
    },
    {
      key: 'brand',
      label: 'Brand',
      type: 'select',
      required: true,
      options: brands.map(brand => ({ value: brand.name, label: brand.name }))
    },
    {
      key: 'segment',
      label: 'Segment',
      type: 'select',
      required: true,
      options: [
        { value: 'Micro', label: 'Micro' },
        { value: 'Compact', label: 'Compact' },
        { value: 'Mid-size', label: 'Mid-size' },
        { value: 'Full-size', label: 'Full-size' },
        { value: 'Luxury', label: 'Luxury' },
        { value: 'Sports', label: 'Sports' },
        { value: 'SUV', label: 'SUV' },
        { value: 'Pickup', label: 'Pickup' }
      ]
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Enter model description',
      description: 'Brief description about the model'
    }
  ]

  return (
    <EntryManager
      title="Model"
      type="model"
      fields={fields}
      data={models}
      onSave={handleSave}
      onDelete={handleDelete}
      icon={<Car size={24} />}
    />
  )
}

export default ModelEntry