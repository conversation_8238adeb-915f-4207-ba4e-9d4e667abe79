// pages/Entries/BrandEntry.js
import React, { useState, useEffect } from 'react'
import { Tag } from 'lucide-react'
import EntryManager from '../EntryManager'

const BrandEntry = () => {
  const [brands, setBrands] = useState([])

  useEffect(() => {
    loadBrands()
  }, [])

  const loadBrands = () => {
    const savedBrands = localStorage.getItem('car_brands')
    if (savedBrands) {
      setBrands(JSON.parse(savedBrands))
    } else {
      const mockBrands = [
        { 
          id: 1, 
          name: 'Toyota', 
          description: 'Japanese automotive manufacturer known for reliability',
          country: 'Japan',
          founded: '1937'
        },
        { 
          id: 2, 
          name: 'Honda', 
          description: 'Japanese multinational automotive manufacturer',
          country: 'Japan',
          founded: '1946'
        },
        { 
          id: 3, 
          name: 'BMW', 
          description: 'German luxury vehicle manufacturer',
          country: 'Germany',
          founded: '1916'
        },
        { 
          id: 4, 
          name: 'Mercedes-Benz', 
          description: 'German luxury automotive brand',
          country: 'Germany',
          founded: '1926'
        }
      ]
      setBrands(mockBrands)
      localStorage.setItem('car_brands', JSON.stringify(mockBrands))
    }
  }

  const handleSave = async (brandData, existingBrand) => {
    let updatedBrands
    if (existingBrand) {
      updatedBrands = brands.map(brand => 
        brand.id === existingBrand.id ? { ...brandData, id: existingBrand.id } : brand
      )
    } else {
      const newBrand = { ...brandData, id: Date.now() }
      updatedBrands = [...brands, newBrand]
    }
    setBrands(updatedBrands)
    localStorage.setItem('car_brands', JSON.stringify(updatedBrands))
  }

  const handleDelete = async (brand) => {
    const updatedBrands = brands.filter(b => b.id !== brand.id)
    setBrands(updatedBrands)
    localStorage.setItem('car_brands', JSON.stringify(updatedBrands))
  }

  const fields = [
    {
      key: 'name',
      label: 'Brand Name',
      type: 'text',
      placeholder: 'Enter brand name',
      required: true
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Enter brand description',
      description: 'Brief description about the brand'
    },
    {
      key: 'country',
      label: 'Country of Origin',
      type: 'select',
      required: true,
      options: [
        { value: 'Japan', label: 'Japan' },
        { value: 'Germany', label: 'Germany' },
        { value: 'USA', label: 'United States' },
        { value: 'South Korea', label: 'South Korea' },
        { value: 'Italy', label: 'Italy' },
        { value: 'France', label: 'France' },
        { value: 'United Kingdom', label: 'United Kingdom' },
        { value: 'Sweden', label: 'Sweden' },
        { value: 'Czech Republic', label: 'Czech Republic' }
      ]
    },
    {
      key: 'founded',
      label: 'Founded Year',
      type: 'number',
      placeholder: 'e.g., 1937',
      min: '1800',
      max: new Date().getFullYear().toString()
    }
  ]

  return (
    <EntryManager
      title="Brand"
      type="brand"
      fields={fields}
      data={brands}
      onSave={handleSave}
      onDelete={handleDelete}
      icon={<Tag size={24} />}
    />
  )
}

export default BrandEntry