// pages/Entries/ConditionEntry.js
import React, { useState, useEffect } from 'react'
import { Star } from 'lucide-react'
import EntryManager from '../EntryManager'

const ConditionEntry = () => {
  const [conditions, setConditions] = useState([])

  useEffect(() => {
    loadConditions()
  }, [])

  const loadConditions = () => {
    const savedConditions = localStorage.getItem('car_conditions')
    if (savedConditions) {
      setConditions(JSON.parse(savedConditions))
    } else {
      const mockConditions = [
        { 
          id: 1, 
          name: 'New', 
          description: 'Brand new vehicle, never used',
          rating: 5,
          depreciation: 0
        },
        { 
          id: 2, 
          name: 'Like New', 
          description: 'Excellent condition, minimal wear',
          rating: 4.5,
          depreciation: 5
        },
        { 
          id: 3, 
          name: 'Excellent', 
          description: 'Very good condition with minor signs of use',
          rating: 4,
          depreciation: 10
        },
        { 
          id: 4, 
          name: 'Good', 
          description: 'Good condition with normal wear and tear',
          rating: 3.5,
          depreciation: 20
        },
        { 
          id: 5, 
          name: 'Fair', 
          description: 'Acceptable condition, may need minor repairs',
          rating: 3,
          depreciation: 35
        },
        { 
          id: 6, 
          name: 'Poor', 
          description: 'Needs significant repairs',
          rating: 2,
          depreciation: 50
        }
      ]
      setConditions(mockConditions)
      localStorage.setItem('car_conditions', JSON.stringify(mockConditions))
    }
  }

  const handleSave = async (conditionData, existingCondition) => {
    let updatedConditions
    if (existingCondition) {
      updatedConditions = conditions.map(condition => 
        condition.id === existingCondition.id ? { ...conditionData, id: existingCondition.id } : condition
      )
    } else {
      const newCondition = { ...conditionData, id: Date.now() }
      updatedConditions = [...conditions, newCondition]
    }
    setConditions(updatedConditions)
    localStorage.setItem('car_conditions', JSON.stringify(updatedConditions))
  }

  const handleDelete = async (condition) => {
    const updatedConditions = conditions.filter(c => c.id !== condition.id)
    setConditions(updatedConditions)
    localStorage.setItem('car_conditions', JSON.stringify(updatedConditions))
  }

  const fields = [
    {
      key: 'name',
      label: 'Condition Name',
      type: 'text',
      placeholder: 'Enter condition name',
      required: true
    },
    {
      key: 'rating',
      label: 'Rating (1-5)',
      type: 'number',
      placeholder: '1-5',
      required: true,
      min: 1,
      max: 5,
      step: 0.1
    },
    {
      key: 'depreciation',
      label: 'Depreciation (%)',
      type: 'number',
      placeholder: 'e.g., 10',
      required: true,
      min: 0,
      max: 100,
      step: 1
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Enter condition description',
      required: true,
      rows: 2
    }
  ]

  return (
    <EntryManager
      title="Condition"
      type="condition"
      fields={fields}
      data={conditions}
      onSave={handleSave}
      onDelete={handleDelete}
      icon={<Star size={24} />}
    />
  )
}

export default ConditionEntry