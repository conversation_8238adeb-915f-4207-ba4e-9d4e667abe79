// pages/Entries/FuelTypeEntry.js
import React, { useState, useEffect } from 'react'
import { Fuel } from 'lucide-react'
import EntryManager from '../EntryManager'

const FuelTypeEntry = () => {
  const [fuelTypes, setFuelTypes] = useState([])

  useEffect(() => {
    loadFuelTypes()
  }, [])

  const loadFuelTypes = () => {
    const savedFuelTypes = localStorage.getItem('car_fuel_types')
    if (savedFuelTypes) {
      setFuelTypes(JSON.parse(savedFuelTypes))
    } else {
      const mockFuelTypes = [
        { 
          id: 1, 
          name: 'Gasoline', 
          description: 'Regular gasoline fuel',
          category: 'Conventional',
          efficiency: 'Medium'
        },
        { 
          id: 2, 
          name: 'Diesel', 
          description: 'Diesel fuel for better mileage',
          category: 'Conventional',
          efficiency: 'High'
        },
        { 
          id: 3, 
          name: 'Hybrid', 
          description: 'Combination of gasoline and electric',
          category: 'Alternative',
          efficiency: 'Very High'
        },
        { 
          id: 4, 
          name: 'Electric', 
          description: 'Fully electric vehicle',
          category: 'Alternative',
          efficiency: 'Maximum'
        },
        { 
          id: 5, 
          name: 'CNG', 
          description: 'Compressed Natural Gas',
          category: 'Alternative',
          efficiency: 'High'
        }
      ]
      setFuelTypes(mockFuelTypes)
      localStorage.setItem('car_fuel_types', JSON.stringify(mockFuelTypes))
    }
  }

  const handleSave = async (fuelTypeData, existingFuelType) => {
    let updatedFuelTypes
    if (existingFuelType) {
      updatedFuelTypes = fuelTypes.map(fuelType => 
        fuelType.id === existingFuelType.id ? { ...fuelTypeData, id: existingFuelType.id } : fuelType
      )
    } else {
      const newFuelType = { ...fuelTypeData, id: Date.now() }
      updatedFuelTypes = [...fuelTypes, newFuelType]
    }
    setFuelTypes(updatedFuelTypes)
    localStorage.setItem('car_fuel_types', JSON.stringify(updatedFuelTypes))
  }

  const handleDelete = async (fuelType) => {
    const updatedFuelTypes = fuelTypes.filter(ft => ft.id !== fuelType.id)
    setFuelTypes(updatedFuelTypes)
    localStorage.setItem('car_fuel_types', JSON.stringify(updatedFuelTypes))
  }

  const fields = [
    {
      key: 'name',
      label: 'Fuel Type Name',
      type: 'text',
      placeholder: 'Enter fuel type name',
      required: true
    },
    {
      key: 'category',
      label: 'Category',
      type: 'select',
      required: true,
      options: [
        { value: 'Conventional', label: 'Conventional' },
        { value: 'Alternative', label: 'Alternative' },
        { value: 'Renewable', label: 'Renewable' }
      ]
    },
    {
      key: 'efficiency',
      label: 'Efficiency Level',
      type: 'select',
      required: true,
      options: [
        { value: 'Low', label: 'Low' },
        { value: 'Medium', label: 'Medium' },
        { value: 'High', label: 'High' },
        { value: 'Very High', label: 'Very High' },
        { value: 'Maximum', label: 'Maximum' }
      ]
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Enter fuel type description',
      rows: 2
    }
  ]

  return (
    <EntryManager
      title="Fuel Type"
      type="fuel-type"
      fields={fields}
      data={fuelTypes}
      onSave={handleSave}
      onDelete={handleDelete}
      icon={<Fuel size={24} />}
    />
  )
}

export default FuelTypeEntry