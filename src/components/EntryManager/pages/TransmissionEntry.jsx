// pages/Entries/TransmissionEntry.js
import React, { useState, useEffect } from 'react'
import { Settings } from 'lucide-react'
import EntryManager from '../EntryManager'

const TransmissionEntry = () => {
  const [transmissions, setTransmissions] = useState([])

  useEffect(() => {
    loadTransmissions()
  }, [])

  const loadTransmissions = () => {
    const savedTransmissions = localStorage.getItem('car_transmissions')
    if (savedTransmissions) {
      setTransmissions(JSON.parse(savedTransmissions))
    } else {
      const mockTransmissions = [
        { 
          id: 1, 
          name: 'Manual', 
          description: 'Manual transmission with clutch pedal',
          type: 'Manual',
          gears: '5-6 Speed'
        },
        { 
          id: 2, 
          name: 'Automatic', 
          description: 'Traditional automatic transmission',
          type: 'Automatic',
          gears: '4-8 Speed'
        },
        { 
          id: 3, 
          name: 'CVT', 
          description: 'Continuously Variable Transmission',
          type: 'Automatic',
          gears: 'Variable'
        },
        { 
          id: 4, 
          name: 'DCT', 
          description: 'Dual-Clutch Transmission',
          type: 'Semi-Automatic',
          gears: '6-7 Speed'
        }
      ]
      setTransmissions(mockTransmissions)
      localStorage.setItem('car_transmissions', JSON.stringify(mockTransmissions))
    }
  }

  const handleSave = async (transmissionData, existingTransmission) => {
    let updatedTransmissions
    if (existingTransmission) {
      updatedTransmissions = transmissions.map(transmission => 
        transmission.id === existingTransmission.id ? { ...transmissionData, id: existingTransmission.id } : transmission
      )
    } else {
      const newTransmission = { ...transmissionData, id: Date.now() }
      updatedTransmissions = [...transmissions, newTransmission]
    }
    setTransmissions(updatedTransmissions)
    localStorage.setItem('car_transmissions', JSON.stringify(updatedTransmissions))
  }

  const handleDelete = async (transmission) => {
    const updatedTransmissions = transmissions.filter(t => t.id !== transmission.id)
    setTransmissions(updatedTransmissions)
    localStorage.setItem('car_transmissions', JSON.stringify(updatedTransmissions))
  }

  const fields = [
    {
      key: 'name',
      label: 'Transmission Name',
      type: 'text',
      placeholder: 'Enter transmission name',
      required: true
    },
    {
      key: 'type',
      label: 'Type',
      type: 'select',
      required: true,
      options: [
        { value: 'Manual', label: 'Manual' },
        { value: 'Automatic', label: 'Automatic' },
        { value: 'Semi-Automatic', label: 'Semi-Automatic' },
        { value: 'CVT', label: 'CVT' }
      ]
    },
    {
      key: 'gears',
      label: 'Gears',
      type: 'text',
      placeholder: 'e.g., 5-Speed, 6-Speed, Variable',
      required: true
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Enter transmission description',
      rows: 2
    }
  ]

  return (
    <EntryManager
      title="Transmission"
      type="transmission"
      fields={fields}
      data={transmissions}
      onSave={handleSave}
      onDelete={handleDelete}
      icon={<Settings size={24} />}
    />
  )
}

export default TransmissionEntry