// pages/Entries/CarTypeEntry.js
import React, { useState, useEffect } from 'react'
import { Truck } from 'lucide-react'
import EntryManager from '../EntryManager'

const CarTypeEntry = () => {
  const [carTypes, setCarTypes] = useState([])

  useEffect(() => {
    loadCarTypes()
  }, [])

  const loadCarTypes = () => {
    const savedCarTypes = localStorage.getItem('car_types')
    if (savedCarTypes) {
      setCarTypes(JSON.parse(savedCarTypes))
    } else {
      const mockCarTypes = [
        { 
          id: 1, 
          name: 'Sedan', 
          description: 'Four-door passenger car with separate trunk',
          category: 'Passenger',
          doors: '4',
          seating: '4-5'
        },
        { 
          id: 2, 
          name: 'SUV', 
          description: 'Sport Utility Vehicle with higher ground clearance',
          category: 'Utility',
          doors: '4-5',
          seating: '5-7'
        },
        { 
          id: 3, 
          name: 'Hatchback', 
          description: 'Compact car with rear door that opens upward',
          category: 'Passenger',
          doors: '3-5',
          seating: '4-5'
        },
        { 
          id: 4, 
          name: 'Pickup', 
          description: 'Truck with open cargo bed',
          category: 'Commercial',
          doors: '2-4',
          seating: '2-5'
        },
        { 
          id: 5, 
          name: 'Coupe', 
          description: 'Two-door sports car',
          category: 'Sports',
          doors: '2',
          seating: '2-4'
        },
        { 
          id: 6, 
          name: 'Convertible', 
          description: 'Car with retractable roof',
          category: 'Sports',
          doors: '2-4',
          seating: '2-4'
        }
      ]
      setCarTypes(mockCarTypes)
      localStorage.setItem('car_types', JSON.stringify(mockCarTypes))
    }
  }

  const handleSave = async (carTypeData, existingCarType) => {
    let updatedCarTypes
    if (existingCarType) {
      updatedCarTypes = carTypes.map(carType => 
        carType.id === existingCarType.id ? { ...carTypeData, id: existingCarType.id } : carType
      )
    } else {
      const newCarType = { ...carTypeData, id: Date.now() }
      updatedCarTypes = [...carTypes, newCarType]
    }
    setCarTypes(updatedCarTypes)
    localStorage.setItem('car_types', JSON.stringify(updatedCarTypes))
  }

  const handleDelete = async (carType) => {
    const updatedCarTypes = carTypes.filter(ct => ct.id !== carType.id)
    setCarTypes(updatedCarTypes)
    localStorage.setItem('car_types', JSON.stringify(updatedCarTypes))
  }

  const fields = [
    {
      key: 'name',
      label: 'Car Type Name',
      type: 'text',
      placeholder: 'Enter car type name',
      required: true
    },
    {
      key: 'category',
      label: 'Category',
      type: 'select',
      required: true,
      options: [
        { value: 'Passenger', label: 'Passenger' },
        { value: 'Sports', label: 'Sports' },
        { value: 'Utility', label: 'Utility' },
        { value: 'Commercial', label: 'Commercial' },
        { value: 'Luxury', label: 'Luxury' },
        { value: 'Electric', label: 'Electric' }
      ]
    },
    {
      key: 'doors',
      label: 'Number of Doors',
      type: 'text',
      placeholder: 'e.g., 2, 4, 3-5',
      required: true
    },
    {
      key: 'seating',
      label: 'Seating Capacity',
      type: 'text',
      placeholder: 'e.g., 2, 5, 5-7',
      required: true
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Enter car type description',
      rows: 2
    }
  ]

  return (
    <EntryManager
      title="Car Type"
      type="car-type"
      fields={fields}
      data={carTypes}
      onSave={handleSave}
      onDelete={handleDelete}
      icon={<Truck size={24} />}
    />
  )
}

export default CarTypeEntry