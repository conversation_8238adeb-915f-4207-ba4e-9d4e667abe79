// pages/Entries/BranchEntry.js
import React, { useState, useEffect } from 'react'
import { MapPin } from 'lucide-react'
import EntryManager from '../EntryManager'

const BranchEntry = () => {
  const [branches, setBranches] = useState([])

  useEffect(() => {
    loadBranches()
  }, [])

  const loadBranches = () => {
    const savedBranches = localStorage.getItem('car_branches')
    if (savedBranches) {
      setBranches(JSON.parse(savedBranches))
    } else {
      const mockBranches = [
        { 
          id: 1, 
          name: 'Bangkok Central', 
          address: '123 Sukhumvit Road, Bangkok',
          phone: '02-123-4567',
          province: 'Bangkok'
        },
        { 
          id: 2, 
          name: 'Chiang Mai North', 
          address: '456 Chang Khlan Road, Chiang Mai',
          phone: '053-123-456',
          province: 'Chiang Mai'
        },
        { 
          id: 3, 
          name: 'Phuket Beach', 
          address: '789 Patong Beach Road, Phuket',
          phone: '076-123-456',
          province: 'Phuket'
        }
      ]
      setBranches(mockBranches)
      localStorage.setItem('car_branches', JSON.stringify(mockBranches))
    }
  }

  const handleSave = async (branchData, existingBranch) => {
    let updatedBranches
    if (existingBranch) {
      updatedBranches = branches.map(branch => 
        branch.id === existingBranch.id ? { ...branchData, id: existingBranch.id } : branch
      )
    } else {
      const newBranch = { ...branchData, id: Date.now() }
      updatedBranches = [...branches, newBranch]
    }
    setBranches(updatedBranches)
    localStorage.setItem('car_branches', JSON.stringify(updatedBranches))
  }

  const handleDelete = async (branch) => {
    const updatedBranches = branches.filter(b => b.id !== branch.id)
    setBranches(updatedBranches)
    localStorage.setItem('car_branches', JSON.stringify(updatedBranches))
  }

  const fields = [
    {
      key: 'name',
      label: 'Branch Name',
      type: 'text',
      placeholder: 'Enter branch name',
      required: true
    },
    {
      key: 'province',
      label: 'Province',
      type: 'select',
      required: true,
      options: [
        { value: 'Bangkok', label: 'Bangkok' },
        { value: 'Chiang Mai', label: 'Chiang Mai' },
        { value: 'Phuket', label: 'Phuket' },
        { value: 'Pattaya', label: 'Pattaya' },
        { value: 'Khon Kaen', label: 'Khon Kaen' },
        { value: 'Nakhon Ratchasima', label: 'Nakhon Ratchasima' },
        { value: 'Hat Yai', label: 'Hat Yai' },
        { value: 'Udon Thani', label: 'Udon Thani' }
      ]
    },
    {
      key: 'address',
      label: 'Address',
      type: 'textarea',
      placeholder: 'Enter full address',
      required: true,
      rows: 2
    },
    {
      key: 'phone',
      label: 'Phone Number',
      type: 'text',
      placeholder: 'e.g., 02-123-4567',
      required: true
    },
    {
      key: 'email',
      label: 'Email',
      type: 'email',
      placeholder: '<EMAIL>'
    }
  ]

  return (
    <EntryManager
      title="Branch"
      type="branch"
      fields={fields}
      data={branches}
      onSave={handleSave}
      onDelete={handleDelete}
      icon={<MapPin size={24} />}
    />
  )
}

export default BranchEntry