// pages/Entries/ColorEntry.js
import React, { useState, useEffect } from 'react'
import { Palette } from 'lucide-react'
import EntryManager from '../EntryManager'

const ColorEntry = () => {
  const [colors, setColors] = useState([])

  useEffect(() => {
    loadColors()
  }, [])

  const loadColors = () => {
    const savedColors = localStorage.getItem('car_colors')
    if (savedColors) {
      setColors(JSON.parse(savedColors))
    } else {
      const mockColors = [
        { id: 1, name: 'Red', colorCode: '#FF0000', category: 'Standard' },
        { id: 2, name: 'Blue', colorCode: '#0000FF', category: 'Standard' },
        { id: 3, name: 'White', colorCode: '#FFFFFF', category: 'Standard' },
        { id: 4, name: 'Black', colorCode: '#000000', category: 'Standard' },
        { id: 5, name: 'Silver', colorCode: '#C0C0C0', category: 'Metallic' },
        { id: 6, name: 'Gold', colorCode: '#FFD700', category: 'Metallic' },
        { id: 7, name: '<PERSON> White', colorCode: '#F8F8FF', category: 'Pearl' }
      ]
      setColors(mockColors)
      localStorage.setItem('car_colors', JSON.stringify(mockColors))
    }
  }

  const handleSave = async (colorData, existingColor) => {
    let updatedColors
    if (existingColor) {
      updatedColors = colors.map(color => 
        color.id === existingColor.id ? { ...colorData, id: existingColor.id } : color
      )
    } else {
      const newColor = { ...colorData, id: Date.now() }
      updatedColors = [...colors, newColor]
    }
    setColors(updatedColors)
    localStorage.setItem('car_colors', JSON.stringify(updatedColors))
  }

  const handleDelete = async (color) => {
    const updatedColors = colors.filter(c => c.id !== color.id)
    setColors(updatedColors)
    localStorage.setItem('car_colors', JSON.stringify(updatedColors))
  }

  const fields = [
    {
      key: 'name',
      label: 'Color Name',
      type: 'text',
      placeholder: 'Enter color name',
      required: true
    },
    {
      key: 'colorCode',
      label: 'Color Code',
      type: 'color',
      placeholder: '#FF0000',
      description: 'Select color or enter hex code'
    },
    {
      key: 'category',
      label: 'Category',
      type: 'select',
      required: true,
      options: [
        { value: 'Standard', label: 'Standard' },
        { value: 'Metallic', label: 'Metallic' },
        { value: 'Pearl', label: 'Pearl' },
        { value: 'Matte', label: 'Matte' },
        { value: 'Custom', label: 'Custom' }
      ]
    },
    {
      key: 'description',
      label: 'Description',
      type: 'textarea',
      placeholder: 'Optional description',
      rows: 2
    }
  ]

  return (
    <EntryManager
      title="Color"
      type="color"
      fields={fields}
      data={colors}
      onSave={handleSave}
      onDelete={handleDelete}
      icon={<Palette size={24} />}
    />
  )
}

export default ColorEntry