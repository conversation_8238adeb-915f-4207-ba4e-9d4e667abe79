// pages/Entries/EntryRouter.js
import React from 'react'
import { useParams, Navigate } from 'react-router-dom'
import BrandEntry from './BrandEntry'
import ColorEntry from './ColorEntry'
import ModelEntry from './ModelEntry'
import BranchEntry from './BranchEntry'
import FuelTypeEntry from './FuelTypeEntry'
import TransmissionEntry from './TransmissionEntry'
import CarTypeEntry from './CarTypeEntry'
import ConditionEntry from './ConditionEntry'
import CCEntry from './CCEntry'

const EntryRouter = () => {
  const { type } = useParams()

  const renderEntryComponent = () => {
    switch (type) {
      case 'brand':
        return <BrandEntry />
      case 'color':
        return <ColorEntry />
      case 'model':
        return <ModelEntry />
      case 'branch':
        return <BranchEntry />
      case 'fuel-type':
        return <FuelTypeEntry />
      case 'transmission':
        return <TransmissionEntry />
      case 'car-type':
        return <CarTypeEntry />
      case 'condition':
        return <ConditionEntry />
      case 'cc':
        return <CCEntry />
      default:
        return <Navigate to="/dashboard" replace />
    }
  }

  return (
    <div className="entry-page">
      {renderEntryComponent()}
    </div>
  )
}

export default EntryRouter