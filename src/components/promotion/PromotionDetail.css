.promotion-detail-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
  animation: fadeIn 0.2s ease-out;
}

.promotion-detail-modal {
  background: white;
  border-radius: 12px;
  max-width: 800px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: slideUp 0.3s ease-out;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  background: white;
  z-index: 10;
}

.detail-header h2 {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
}

.detail-content {
  padding: 24px;
}

.detail-image {
  width: 100%;
  height: 300px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 24px;
}

.detail-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.info-section {
  margin-bottom: 32px;
}

.info-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #667eea;
}

.description {
  font-size: 16px;
  color: #374151;
  line-height: 1.6;
  margin-bottom: 8px;
}

.description-kh {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.6;
}

.offer-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.offer-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.offer-item svg {
  color: #667eea;
  flex-shrink: 0;
}

.offer-label {
  display: block;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.offer-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.offer-value.status-active {
  color: #10b981;
}

.offer-value.status-upcoming {
  color: #3b82f6;
}

.offer-value.status-expired {
  color: #6b7280;
}

.brand-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.brand-tag {
  padding: 6px 16px;
  background: #667eea;
  color: white;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.benefits-list,
.terms-list,
.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.benefits-list li,
.terms-list li,
.requirements-list li {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  font-size: 14px;
  color: #374151;
}

.benefits-list svg {
  color: #10b981;
  flex-shrink: 0;
  margin-top: 2px;
}

.terms-list svg {
  color: #f59e0b;
  flex-shrink: 0;
  margin-top: 2px;
}

.requirements-list svg {
  color: #3b82f6;
  flex-shrink: 0;
  margin-top: 2px;
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-top: 32px;
}

.apply-btn {
  flex: 1;
  padding: 14px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.apply-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.share-btn {
  padding: 14px 24px;
  background: white;
  color: #667eea;
  border: 2px solid #667eea;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.share-btn:hover {
  background: #667eea;
  color: white;
}

/* Dark mode */
.dark .promotion-detail-modal {
  background: var(--bg-secondary);
}

.dark .detail-header {
  background: var(--bg-secondary);
  border-bottom-color: var(--border-primary);
}

.dark .detail-header h2 {
  color: var(--text-primary);
}

.dark .close-btn {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.dark .info-section h3 {
  color: var(--text-primary);
  border-bottom-color: #818cf8;
}

.dark .description {
  color: var(--text-primary);
}

.dark .description-kh {
  color: var(--text-secondary);
}

.dark .offer-item {
  background: var(--bg-tertiary);
  border-color: var(--border-primary);
}

.dark .offer-label {
  color: var(--text-secondary);
}

.dark .offer-value {
  color: var(--text-primary);
}

.dark .brand-tag {
  background: #818cf8;
}

.dark .benefits-list li,
.dark .terms-list li,
.dark .requirements-list li {
  color: var(--text-primary);
}

.dark .share-btn {
  background: var(--bg-tertiary);
  border-color: #818cf8;
  color: #818cf8;
}

.dark .share-btn:hover {
  background: #818cf8;
  color: white;
}

@media (max-width: 768px) {
  .promotion-detail-modal {
    max-height: 100vh;
    border-radius: 0;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .share-btn {
    width: 100%;
  }
}