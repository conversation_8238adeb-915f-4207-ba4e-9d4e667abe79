import { X, Calendar, Clock, Tag, Gift, CheckCircle, AlertCircle } from 'lucide-react'
import './PromotionDetail.css'

const PromotionDetail = ({ promotion, onClose }) => {
  if (!promotion) return null

  return (
    <div className="promotion-detail-overlay" onClick={onClose}>
      <div className="promotion-detail-modal" onClick={(e) => e.stopPropagation()}>
        <div className="detail-header">
          <h2>{promotion.title}</h2>
          <button className="close-btn" onClick={onClose}>
            <X size={24} />
          </button>
        </div>

        <div className="detail-content">
          <div className="detail-image">
            <img src={promotion.image} alt={promotion.title} />
          </div>

          <div className="detail-info">
            <div className="info-section">
              <h3>Promotion Details</h3>
              <p className="description">{promotion.description}</p>
              <p className="description-kh">{promotion.descriptionKH}</p>
            </div>

            <div className="info-section">
              <h3>Offer Details</h3>
              <div className="offer-grid">
                {promotion.discountValue && (
                  <div className="offer-item">
                    <Tag size={20} />
                    <div>
                      <span className="offer-label">Discount</span>
                      <span className="offer-value">
                        {promotion.discountType === 'percentage' ? 
                          `${promotion.discountValue}%` : 
                          `$${promotion.discountValue}`} OFF
                      </span>
                    </div>
                  </div>
                )}

                {promotion.minimumPurchase && (
                  <div className="offer-item">
                    <Gift size={20} />
                    <div>
                      <span className="offer-label">Minimum Purchase</span>
                      <span className="offer-value">${promotion.minimumPurchase.toLocaleString()}</span>
                    </div>
                  </div>
                )}

                <div className="offer-item">
                  <Calendar size={20} />
                  <div>
                    <span className="offer-label">Valid Period</span>
                    <span className="offer-value">
                      {new Date(promotion.startDate).toLocaleDateString()} - {new Date(promotion.endDate).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="offer-item">
                  <Clock size={20} />
                  <div>
                    <span className="offer-label">Status</span>
                    <span className={`offer-value status-${promotion.status}`}>
                      {promotion.status}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {promotion.applicableBrands && (
              <div className="info-section">
                <h3>Applicable Brands</h3>
                <div className="brand-tags">
                  {promotion.applicableBrands.map(brand => (
                    <span key={brand} className="brand-tag">{brand}</span>
                  ))}
                </div>
              </div>
            )}

            {promotion.benefits && (
              <div className="info-section">
                <h3>Benefits</h3>
                <ul className="benefits-list">
                  {promotion.benefits.map((benefit, index) => (
                    <li key={index}>
                      <CheckCircle size={16} />
                      {benefit}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {promotion.terms && (
              <div className="info-section">
                <h3>Terms & Conditions</h3>
                <ul className="terms-list">
                  {promotion.terms.map((term, index) => (
                    <li key={index}>
                      <AlertCircle size={16} />
                      {term}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {promotion.requirements && (
              <div className="info-section">
                <h3>Requirements</h3>
                <ul className="requirements-list">
                  {promotion.requirements.map((req, index) => (
                    <li key={index}>
                      <CheckCircle size={16} />
                      {req}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            <div className="action-buttons">
              <button className="apply-btn">
                Apply This Promotion
              </button>
              <button className="share-btn">
                Share
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PromotionDetail