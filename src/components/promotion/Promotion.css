.promotion-container {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
}

.promotion-header {
  text-align: center;
  margin-bottom: 40px;
}

.header-content h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 8px;
}

.header-content p {
  font-size: 16px;
  color: #6b7280;
}

.promotion-stats {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 32px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 32px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.stat-icon.active {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.stat-icon.upcoming {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.stat-icon.saved {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-info h3 {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0;
}

.stat-info p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

.promotion-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  gap: 24px;
}

.search-bar {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-bar svg {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
}

.search-bar input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s;
}

.search-bar input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-tabs {
  display: flex;
  gap: 12px;
}

.filter-tab {
  padding: 8px 16px;
  border: 2px solid #e5e7eb;
  background: white;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  color: #6b7280;
}

.filter-tab:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-tab.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.promotions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.promotion-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  position: relative;
}

.promotion-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
}

.promotion-card.featured {
  border: 2px solid #fbbf24;
  box-shadow: 0 4px 12px rgba(251, 191, 36, 0.2);
}

.featured-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  z-index: 10;
}

.promotion-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.promotion-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.promotion-status {
  position: absolute;
  top: 16px;
  left: 16px;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-active {
  background: rgba(16, 185, 129, 0.9);
  color: white;
}

.status-upcoming {
  background: rgba(59, 130, 246, 0.9);
  color: white;
}

.status-expired {
  background: rgba(107, 114, 128, 0.9);
  color: white;
}

.promotion-content {
  padding: 24px;
}

.promotion-header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.promotion-type {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
  text-transform: capitalize;
}

.days-left {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #ef4444;
  font-weight: 500;
}

.promotion-title {
  font-size: 20px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 4px 0;
}

.promotion-subtitle {
  font-size: 14px;
  color: #6b7280;
  margin: 0 0 12px 0;
}

.promotion-description {
  font-size: 14px;
  color: #374151;
  margin-bottom: 16px;
  line-height: 1.5;
}

.promotion-details {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
  padding: 4px 12px;
  background: #f3f4f6;
  border-radius: 20px;
}

.detail-item.highlight {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  font-weight: 600;
}

.usage-progress {
  margin-bottom: 20px;
}

.usage-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
}

.progress-bar {
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  transition: width 0.3s ease;
}

.view-details-btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
}

.view-details-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.no-promotions {
  text-align: center;
  padding: 80px 20px;
  color: #6b7280;
}

.no-promotions svg {
  color: #d1d5db;
  margin-bottom: 16px;
}

.no-promotions h3 {
  font-size: 20px;
  color: #374151;
  margin-bottom: 8px;
}

.promotion-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* Dark mode styles */
.dark .promotion-container {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.dark .header-content h1 {
  color: var(--text-primary);
}

.dark .header-content p {
  color: var(--text-secondary);
}

.dark .stat-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
}

.dark .stat-info h3 {
  color: var(--text-primary);
}

.dark .stat-info p {
  color: var(--text-secondary);
}

.dark .search-bar input {
  background: var(--bg-secondary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .filter-tab {
  background: var(--bg-secondary);
  border-color: var(--border-primary);
  color: var(--text-secondary);
}

.dark .filter-tab:hover {
  border-color: #818cf8;
  color: #818cf8;
}

.dark .filter-tab.active {
  background: #818cf8;
  border-color: #818cf8;
}

.dark .promotion-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
}

.dark .promotion-card.featured {
  border-color: #fbbf24;
}

.dark .promotion-title {
  color: var(--text-primary);
}

.dark .promotion-subtitle {
  color: var(--text-secondary);
}

.dark .promotion-description {
  color: var(--text-secondary);
}

.dark .detail-item {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.dark .progress-bar {
  background: var(--bg-tertiary);
}

.dark .view-details-btn {
  background: linear-gradient(135deg, #818cf8 0%, #a78bfa 100%);
}

.dark .no-promotions {
  color: var(--text-secondary);
}

.dark .no-promotions h3 {
  color: var(--text-primary);
}

/* Responsive */
@media (max-width: 768px) {
  .promotion-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .stat-card {
    width: 100%;
  }
  
  .promotion-controls {
    flex-direction: column;
  }
  
  .search-bar {
    max-width: 100%;
  }
  
  .filter-tabs {
    width: 100%;
    justify-content: space-between;
  }
  
  .promotions-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}