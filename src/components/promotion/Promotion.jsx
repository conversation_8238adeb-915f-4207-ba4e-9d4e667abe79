import { useState, useEffect } from 'react'
import { 
  Tag, 
  Calendar, 
  Clock, 
  Percent, 
  Gift, 
  Star, 
  ChevronRight,
  Filter,
  Search,
  TrendingUp,
  Users,
  Award,
  Target
} from 'lucide-react'
import './Promotion.css'
import PromotionDetail from './PromotionDetail'


const Promotion = () => {
  const [promotions, setPromotions] = useState([])
  const [activeFilter, setActiveFilter] = useState('all')
  const [searchTerm, setSearchTerm] = useState('')
  const [loading, setLoading] = useState(true)

  const [selectedPromotion, setSelectedPromotion] = useState(null)

  const handleViewDetails = (promotion) => {
    setSelectedPromotion(promotion)
  }

  
  // Mock data - ในอนาคตจะดึงจาก API
  const mockPromotions = [
    {
      id: 1,
      title: 'Year End Sale 2024',
      titleKH: 'ការលក់ចុងឆ្នាំ ២០២៤',
      description: 'Get up to 20% discount on selected vehicles',
      descriptionKH: 'ទទួលបានការបញ្ចុះតម្លៃរហូតដល់ 20% លើរថយន្តដែលបានជ្រើសរើស',
      type: 'discount',
      discountType: 'percentage',
      discountValue: 20,
      minimumPurchase: 10000,
      startDate: '2024-01-01',
      endDate: '2024-01-31',
      status: 'active',
      image: 'https://via.placeholder.com/600x300?text=Year+End+Sale',
      applicableBrands: ['Toyota', 'Honda', 'BMW'],
      usageCount: 156,
      maxUsage: 500,
      featured: true,
      terms: [
        'Valid for selected models only',
        'Cannot be combined with other promotions',
        'Subject to availability'
      ]
    },
    {
      id: 2,
      title: 'First Time Buyer Special',
      titleKH: 'ពិសេសសម្រាប់អ្នកទិញលើកដំបូង',
      description: 'Special financing rates for first-time buyers',
      descriptionKH: 'អត្រាហិរញ្ញប្បទានពិសេសសម្រាប់អ្នកទិញលើកដំបូង',
      type: 'financing',
      interestRate: 2.99,
      downPayment: 10,
      startDate: '2024-01-15',
      endDate: '2024-02-15',
      status: 'active',
      image: 'https://via.placeholder.com/600x300?text=First+Time+Buyer',
      usageCount: 89,
      maxUsage: 200,
      featured: true,
      benefits: [
        'Low interest rate 2.99% per year',
        'Minimum down payment 10%',
        'Free insurance for 1 year',
        'Extended warranty'
      ]
    },
    {
      id: 3,
      title: 'Trade-In Bonus',
      titleKH: 'ប្រាក់រង្វាន់ការដោះដូរ',
      description: 'Extra $500 when you trade in your old vehicle',
      descriptionKH: 'ប្រាក់បន្ថែម $500 នៅពេលអ្នកដោះដូររថចាស់របស់អ្នក',
      type: 'trade-in',
      bonusAmount: 500,
      startDate: '2024-01-10',
      endDate: '2024-03-31',
      status: 'active',
      image: 'https://via.placeholder.com/600x300?text=Trade+In+Bonus',
      applicableTypes: ['SUV', 'Sedan', 'Pickup'],
      usageCount: 234,
      maxUsage: 1000,
      requirements: [
        'Vehicle must be 2015 or newer',
        'Must pass inspection',
        'Clear ownership documents'
      ]
    },
    {
      id: 4,
      title: 'Weekend Flash Sale',
      titleKH: 'ការលក់ភ្លាមៗចុងសប្តាហ៍',
      description: 'Special prices every weekend',
      descriptionKH: 'តម្លៃពិសេសរៀងរាល់ចុងសប្តាហ៍',
      type: 'flash-sale',
      discountType: 'fixed',
      discountValue: 1000,
      startDate: '2024-01-20',
      endDate: '2024-01-21',
      status: 'upcoming',
      image: 'https://via.placeholder.com/600x300?text=Flash+Sale',
      timeSlots: ['10:00-12:00', '14:00-16:00'],
      featured: false
    },
    {
      id: 5,
      title: 'Loyalty Program Rewards',
      titleKH: 'រង្វាន់កម្មវិធីភក្តីភាព',
      description: 'Exclusive benefits for our loyal customers',
      descriptionKH: 'អត្ថប្រយោជន៍ផ្តាច់មុខសម្រាប់អតិថិជនស្មោះត្រង់របស់យើង',
      type: 'loyalty',
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      status: 'active',
      image: 'https://via.placeholder.com/600x300?text=Loyalty+Program',
      tiers: [
        { name: 'Silver', discount: 5, minPurchase: 5000 },
        { name: 'Gold', discount: 10, minPurchase: 20000 },
        { name: 'Platinum', discount: 15, minPurchase: 50000 }
      ]
    }
  ]

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setPromotions(mockPromotions)
      setLoading(false)
    }, 1000)
  }, [])

  const filterPromotions = () => {
    let filtered = [...promotions]

    // Filter by status
    if (activeFilter !== 'all') {
      filtered = filtered.filter(promo => promo.status === activeFilter)
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(promo =>
        promo.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        promo.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // Sort featured first
    filtered.sort((a, b) => {
      if (a.featured && !b.featured) return -1
      if (!a.featured && b.featured) return 1
      return 0
    })

    return filtered
  }

  const getPromotionIcon = (type) => {
    switch (type) {
      case 'discount':
        return <Percent size={20} />
      case 'financing':
        return <TrendingUp size={20} />
      case 'trade-in':
        return <Users size={20} />
      case 'flash-sale':
        return <Clock size={20} />
      case 'loyalty':
        return <Award size={20} />
      default:
        return <Gift size={20} />
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'status-active'
      case 'upcoming':
        return 'status-upcoming'
      case 'expired':
        return 'status-expired'
      default:
        return ''
    }
  }

  const calculateDaysLeft = (endDate) => {
    const today = new Date()
    const end = new Date(endDate)
    const diffTime = Math.abs(end - today)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  if (loading) {
    return (
      <div className="promotion-loading">
        <div className="loading-spinner"></div>
        <p>Loading promotions...</p>
      </div>
    )
  }

  const filteredPromotions = filterPromotions()
  const stats = {
    active: promotions.filter(p => p.status === 'active').length,
    upcoming: promotions.filter(p => p.status === 'upcoming').length,
    totalSaved: promotions.reduce((sum, p) => sum + (p.usageCount || 0) * (p.discountValue || p.bonusAmount || 0), 0)
  }

  return (
    <div className="promotion-container">
      <div className="promotion-header">
        <div className="header-content">
          <h1>Special Promotions</h1>
          <p>Don't miss out on our exclusive deals and offers</p>
        </div>
        
        <div className="promotion-stats">
          <div className="stat-card">
            <div className="stat-icon active">
              <Target size={24} />
            </div>
            <div className="stat-info">
              <h3>{stats.active}</h3>
              <p>Active Deals</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon upcoming">
              <Clock size={24} />
            </div>
            <div className="stat-info">
              <h3>{stats.upcoming}</h3>
              <p>Coming Soon</p>
            </div>
          </div>
          <div className="stat-card">
            <div className="stat-icon saved">
              <Percent size={24} />
            </div>
            <div className="stat-info">
              <h3>${stats.totalSaved.toLocaleString()}</h3>
              <p>Total Saved</p>
            </div>
          </div>
        </div>
      </div>

      <div className="promotion-controls">
        <div className="search-bar">
          <Search size={20} />
          <input
            type="text"
            placeholder="Search promotions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="filter-tabs">
          <button
            className={`filter-tab ${activeFilter === 'all' ? 'active' : ''}`}
            onClick={() => setActiveFilter('all')}
          >
            All Promotions
          </button>
          <button
            className={`filter-tab ${activeFilter === 'active' ? 'active' : ''}`}
            onClick={() => setActiveFilter('active')}
          >
            Active
          </button>
          <button
            className={`filter-tab ${activeFilter === 'upcoming' ? 'active' : ''}`}
            onClick={() => setActiveFilter('upcoming')}
          >
            Coming Soon
          </button>
        </div>
      </div>

      <div className="promotions-grid">
        {filteredPromotions.map(promotion => (
          <div key={promotion.id} className={`promotion-card ${promotion.featured ? 'featured' : ''}`}>
            {promotion.featured && (
              <div className="featured-badge">
                <Star size={14} />
                Featured
              </div>
            )}
            
            <div className="promotion-image">
              <img src={promotion.image} alt={promotion.title} />
              <div className={`promotion-status ${getStatusColor(promotion.status)}`}>
                {promotion.status}
              </div>
            </div>

            <div className="promotion-content">
              <div className="promotion-header-info">
                <div className="promotion-type">
                  {getPromotionIcon(promotion.type)}
                  <span>{promotion.type.replace('-', ' ')}</span>
                </div>
                {promotion.status === 'active' && (
                  <div className="days-left">
                    <Clock size={14} />
                    {calculateDaysLeft(promotion.endDate)} days left
                  </div>
                )}
              </div>

              <h3 className="promotion-title">{promotion.title}</h3>
              <p className="promotion-subtitle">{promotion.titleKH}</p>
              <p className="promotion-description">{promotion.description}</p>

              <div className="promotion-details">
                {promotion.discountValue && (
                  <div className="detail-item highlight">
                    <Percent size={16} />
                    {promotion.discountType === 'percentage' ? 
                      `${promotion.discountValue}% OFF` : 
                      `$${promotion.discountValue} OFF`
                    }
                  </div>
                )}
                
                {promotion.interestRate && (
                  <div className="detail-item highlight">
                    <TrendingUp size={16} />
                    {promotion.interestRate}% APR
                  </div>
                )}

                {promotion.bonusAmount && (
                  <div className="detail-item highlight">
                    <Gift size={16} />
                    ${promotion.bonusAmount} Bonus
                  </div>
                )}

                <div className="detail-item">
                  <Calendar size={16} />
                  {new Date(promotion.startDate).toLocaleDateString()} - {new Date(promotion.endDate).toLocaleDateString()}
                </div>
              </div>

              {promotion.usageCount && promotion.maxUsage && (
                <div className="usage-progress">
                  <div className="usage-info">
                    <span>Used: {promotion.usageCount}/{promotion.maxUsage}</span>
                    <span>{Math.round((promotion.usageCount / promotion.maxUsage) * 100)}%</span>
                  </div>
                  <div className="progress-bar">
                    <div 
                      className="progress-fill"
                      style={{ width: `${(promotion.usageCount / promotion.maxUsage) * 100}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <button className="view-details-btn">
                View Details
                <ChevronRight size={16} />
              </button>
            </div>
          </div>
        ))}
      </div>

      {filteredPromotions.length === 0 && (
        <div className="no-promotions">
          <Gift size={48} />
          <h3>No promotions found</h3>
          <p>Try adjusting your filters or check back later</p>
        </div>
      )}
    </div>
  )
}

export default Promotion