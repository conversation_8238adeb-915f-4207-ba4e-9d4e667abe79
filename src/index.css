body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Dark Mode Variables */
:root {
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1a1a1a;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --shadow: rgba(0, 0, 0, 0.1);
}

.dark {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --text-primary: #f1f5f9;
  --text-secondary: #94a3b8;
  --border-color: #334155;
  --shadow: rgba(0, 0, 0, 0.4);
}

/* Dark Mode Global Styles */
.dark body {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.dark .header {
  background: var(--bg-secondary);
  border-bottom-color: var(--border-color);
  box-shadow: 0 2px 10px var(--shadow);
}

.dark .logo-text {
  color: var(--text-primary);
}

.dark .search-input {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.dark .search-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2);
}

.dark .search-input::placeholder {
  color: var(--text-secondary);
}

.dark .user-menu-trigger {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.dark .user-menu-dropdown {
  background: var(--bg-secondary);
  border-color: var(--border-color);
  box-shadow: 0 4px 12px var(--shadow);
}

.dark .user-name {
  color: var(--text-primary);
}

.dark .user-email {
  color: var(--text-secondary);
}

.dark .menu-item {
  color: var(--text-primary);
}

.dark .menu-item:hover {
  background: var(--bg-primary);
}

/* Car Cards Dark Mode */
.dark .car-card {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

.dark .car-title {
  color: var(--text-primary);
}

.dark .car-meta {
  border-top-color: var(--border-color);
}

.dark .seller-name {
  color: var(--text-primary);
}

.dark .car-date {
  color: var(--text-secondary);
}

/* Filter Panel Dark Mode */
.dark .filter-panel {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

.dark .filter-panel h3 {
  color: var(--text-primary);
}

.dark .filter-panel label {
  color: var(--text-primary);
}

.dark .filter-panel select,
.dark .filter-panel input {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.dark .filter-panel select:focus,
.dark .filter-panel input:focus {
  border-color: #667eea;
}

/* Modal Dark Mode */
.dark .car-form-container,
.dark .car-detail-container {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

.dark .car-form-header h2,
.dark .car-detail-header h2 {
  color: var(--text-primary);
}

.dark .form-section h3,
.dark .specifications h3,
.dark .description h3,
.dark .seller-info h3 {
  color: var(--text-primary);
  border-bottom-color: #667eea;
}

.dark .form-group label,
.dark .spec-label {
  color: var(--text-primary);
}

.dark .form-group input,
.dark .form-group select,
.dark .form-group textarea {
  background: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

.dark .close-btn {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.dark .close-btn:hover {
  background: var(--border-color);
}

/* Loading & Empty States Dark Mode */
.dark .loading-container,
.dark .empty-state {
  color: var(--text-primary);
}

.dark .empty-state h3 {
  color: var(--text-primary);
}

.dark .empty-state p {
  color: var(--text-secondary);
}