/* override.css */
:root {
  /* Override ตัวแปร CSS ให้เป็นสีจากโปรเจคเก่า */
  --theme-primary: #1C466E;
  --theme-primary-hover: rgba(28, 70, 110, 0.9);
  --theme-primary-light: rgba(28, 70, 110, 0.2);
  --theme-primary-lighter: rgba(28, 70, 110, 0.05);

}

/* คุณสามารถใช้ตัวแปร หรือถ้าไม่มีการใช้ตัวแปร ก็ใส่สีตรงๆได้เลย */
body {
  background-color: var(--theme-primary-light) !important;
  color: var(--theme-primary) !important;
}

/* ตัวอย่างการกำหนดสีสำหรับ element อื่น */
.themeColor {
  background-color: var(--theme-primary) !important;
  color: rgb(255, 255, 255) !important;
}