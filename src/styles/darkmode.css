/* ===== BASE DARK MODE STYLES ===== */
.dark {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-primary: #334155;
  --border-secondary: #475569;
  --shadow-sm: rgba(0, 0, 0, 0.2);
  --shadow-md: rgba(0, 0, 0, 0.4);
  --shadow-lg: rgba(0, 0, 0, 0.6);
  --overlay: rgba(0, 0, 0, 0.7);
  
  /* Dark theme colors */
  --theme-primary: #4a7ba7;
  --theme-primary-hover: #5a8bb7;
  --theme-primary-light: rgba(74, 123, 167, 0.2);
  --theme-primary-lighter: rgba(74, 123, 167, 0.1);
}

/* ===== GLOBAL DARK MODE ===== */
.dark body {
  background: var(--bg-primary);
  color: var(--text-primary);
}

/* ===== DASHBOARD DARK MODE ===== */
.dark .dashboard {
  background: var(--bg-primary);
}

.dark .dashboard-main {
  background: var(--bg-primary);
}

/* ===== HEADER DARK MODE ===== */
.dark .header {
  background: var(--bg-secondary);
  border-bottom-color: var(--border-primary);
  box-shadow: 0 2px 10px var(--shadow-md);
}

.dark .logo-text {
  color: var(--text-primary);
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-hover) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.dark .search-input {
  background: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .search-input:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px var(--theme-primary-light);
}

.dark .search-input::placeholder {
  color: var(--text-tertiary);
}

.dark .search-icon {
  color: var(--text-secondary);
}

.dark .add-car-btn {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-hover) 100%);
}

.dark .add-car-btn:hover {
  box-shadow: 0 4px 12px rgba(74, 123, 167, 0.4);
}

.dark .user-menu-trigger {
  background: var(--bg-tertiary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .user-menu-trigger:hover {
  border-color: var(--theme-primary);
  background: var(--bg-primary);
}

.dark .user-menu-dropdown {
  background: var(--bg-secondary);
  border-color: var(--border-primary);
  box-shadow: 0 4px 12px var(--shadow-lg);
}

.dark .user-menu-dropdown hr {
  border-top-color: var(--border-primary);
}

.dark .menu-item {
  color: var(--text-primary);
}

.dark .menu-item:hover {
  background: var(--bg-tertiary);
}

/* ===== CAR LIST DARK MODE ===== */
.dark .car-list-container {
  background: var(--bg-primary);
  color: var(--text-primary);
}

.dark .car-list-header h1 {
  color: var(--text-primary);
}

.dark .filter-btn {
  background: var(--bg-secondary);
  border-color: var(--border-primary);
  color: var(--text-secondary);
}

.dark .filter-btn:hover,
.dark .filter-btn.active {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
  background: var(--bg-tertiary);
}

.dark .view-controls {
  border-color: var(--border-primary);
  background: var(--bg-secondary);
}

.dark .view-btn {
  background: var(--bg-secondary);
  color: var(--text-secondary);
}

.dark .view-btn:hover,
.dark .view-btn.active {
  background: var(--theme-primary);
  color: white;
}

.dark .empty-state {
  color: var(--text-secondary);
}

.dark .empty-state h3 {
  color: var(--text-primary);
}

.dark .empty-state p {
  color: var(--text-secondary);
}

.dark .add-first-car-btn {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-hover) 100%);
}

/* ===== CAR CARD DARK MODE ===== */
.dark .car-card {
  background: var(--bg-secondary);
  box-shadow: 0 4px 6px var(--shadow-sm);
  border: 1px solid var(--border-primary);
}

.dark .car-card:hover {
  box-shadow: 0 8px 20px var(--shadow-md);
  border-color: var(--theme-primary);
}

.dark .car-title {
  color: var(--text-primary);
}

.dark .car-price {
  color: var(--theme-primary);
}

.dark .car-price-container .car-price:hover {
  color: var(--theme-primary-hover);
}

.dark .original-price-note {
  color: var(--text-tertiary);
}

.dark .detail-item {
  color: var(--text-secondary);
}

.dark .detail-item svg {
  color: var(--theme-primary);
}

.dark .car-meta {
  border-top-color: var(--border-primary);
}

.dark .seller-name {
  color: var(--text-secondary);
}

.dark .car-date {
  color: var(--text-tertiary);
}

.dark .condition-badge {
  border: 1px solid var(--border-primary);
}

.dark .condition-badge.new {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border-color: #10b981;
}

.dark .condition-badge.used {
  background: rgba(245, 158, 11, 0.2);
  color: #f59e0b;
  border-color: #f59e0b;
}

.dark .action-btn {
  backdrop-filter: blur(10px);
}

.dark .action-btn.view {
  background: rgba(28, 70, 110, 0.8);
}

.dark .action-btn.edit {
  background: rgba(16, 185, 129, 0.8);
}

.dark .action-btn.delete {
  background: rgba(239, 68, 68, 0.8);
}

/* ===== CAR FORM DARK MODE ===== */
.dark .car-form-container {
  background: var(--bg-secondary);
  box-shadow: 0 20px 40px var(--shadow-lg);
}

.dark .car-form-header {
  background: var(--bg-secondary);
  border-bottom-color: var(--border-primary);
}

.dark .car-form-header h2 {
  color: var(--text-primary);
}

.dark .close-btn {
  background: var(--bg-tertiary);
  color: var(--text-primary);
}

.dark .close-btn:hover {
  background: var(--border-primary);
}

.dark .form-section {
  color: var(--text-primary);
}

.dark .form-section h3 {
  color: var(--text-primary);
  border-bottom-color: var(--theme-primary);
}

.dark .form-group label {
  color: var(--text-secondary);
}

.dark .form-group input,
.dark .form-group select,
.dark .form-group textarea {
  background: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .form-group input:focus,
.dark .form-group select:focus,
.dark .form-group textarea:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px var(--theme-primary-light);
}

.dark .form-group input::placeholder,
.dark .form-group textarea::placeholder {
  color: var(--text-tertiary);
}

.dark .form-group input.error,
.dark .form-group select.error,
.dark .form-group textarea.error {
  border-color: #ef4444;
}

.dark .error-text {
  color: #f87171;
}

.dark .image-input-group input {
  background: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .remove-image-btn {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.dark .remove-image-btn:hover {
  background: rgba(239, 68, 68, 0.3);
}

.dark .add-image-btn {
  border-color: var(--border-secondary);
  color: var(--text-secondary);
}

.dark .add-image-btn:hover {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
  background: var(--theme-primary-lighter);
}

.dark .form-actions {
  border-top-color: var(--border-primary);
}

.dark .cancel-btn {
  background: var(--bg-tertiary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .cancel-btn:hover {
  background: var(--bg-primary);
  border-color: var(--border-secondary);
}

.dark .save-btn {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-hover) 100%);
}

.dark .save-btn:hover {
  box-shadow: 0 4px 12px rgba(74, 123, 167, 0.4);
}

/* ===== CAR DETAIL DARK MODE ===== */
.dark .car-detail-container {
  background: var(--bg-secondary);
  box-shadow: 0 20px 40px var(--shadow-lg);
}

.dark .car-detail-header {
  background: var(--bg-secondary);
  border-bottom-color: var(--border-primary);
}

.dark .car-detail-header h2 {
  color: var(--text-primary);
}

.dark .edit-btn {
  background: #10b981;
}

.dark .edit-btn:hover {
  background: #059669;
}

.dark .price-section {
  background: var(--bg-tertiary);
  border-color: var(--border-primary);
}

.dark .price-display.usd {
  color: #10b981;
}

.dark .price-display.khr {
  color: #f59e0b;
}

.dark .currency-toggle {
  background: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .currency-toggle:hover {
  background: var(--bg-secondary);
  border-color: var(--border-secondary);
}

.dark .alternative-price,
.dark .exchange-rate {
  color: var(--text-secondary);
}

.dark .basic-info {
  background: var(--bg-tertiary);
  border-color: var(--border-primary);
}

.dark .info-item {
  color: var(--text-secondary);
}

.dark .info-item svg {
  color: var(--theme-primary);
}

.dark .specifications,
.dark .description,
.dark .seller-info {
  background: var(--bg-secondary);
  border-color: var(--border-primary);
}

.dark .specifications h3,
.dark .description h3,
.dark .seller-info h3 {
  color: var(--text-primary);
  border-bottom-color: var(--theme-primary);
}

.dark .spec-item {
  background: var(--bg-tertiary);
  border-color: var(--border-primary);
}

.dark .spec-item:hover {
  background: var(--bg-primary);
  border-color: var(--border-secondary);
}

.dark .spec-label {
  color: var(--text-secondary);
}

.dark .spec-value {
  color: var(--text-primary);
}

.dark .description p {
  color: var(--text-secondary);
}

.dark .seller-info {
  background: var(--bg-tertiary);
  border-color: var(--theme-primary);
}

.dark .seller-details {
  background: var(--bg-secondary);
  border-color: var(--border-primary);
}

.dark .seller-name {
  color: var(--text-primary);
}

.dark .contact-item {
  background: var(--bg-primary);
  color: var(--text-secondary);
}

.dark .contact-item:hover {
  background: var(--bg-tertiary);
}

.dark .listing-info {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  border-top-color: var(--border-primary);
}

/* ===== FILTER PANEL DARK MODE ===== */
.dark .filter-panel {
  background: var(--bg-secondary);
  box-shadow: 0 2px 8px var(--shadow-md);
  border: 1px solid var(--border-primary);
}

.dark .filter-header h3 {
  color: var(--text-primary);
}

.dark .clear-btn {
  color: var(--theme-primary);
}

.dark .clear-btn:hover {
  color: var(--theme-primary-hover);
}

.dark .filter-group label {
  color: var(--text-secondary);
}

.dark .filter-group select,
.dark .filter-group input {
  background: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .filter-group select:focus,
.dark .filter-group input:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px var(--theme-primary-light);
}

.dark .price-range span {
  color: var(--text-secondary);
}

.dark .price-examples small {
  color: var(--text-tertiary);
}

.dark .exchange-note {
  color: var(--theme-primary);
}

.dark .price-filter-header {
  color: var(--text-primary);
}

.dark .currency-toggle-small {
  background: var(--bg-tertiary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .currency-toggle-small:hover {
  background: var(--bg-primary);
  border-color: var(--border-secondary);
}

/* ===== MODAL OVERLAY DARK MODE ===== */
.dark .modal-overlay {
  background: var(--overlay);
}

/* ===== LOADING STATES DARK MODE ===== */
.dark .loading-container {
  color: var(--text-primary);
}

.dark .loading-spinner {
  border-color: var(--border-primary);
  border-top-color: var(--theme-primary);
}

/* ===== LOGIN PAGE DARK MODE ===== */
.dark .login-wrapper {
  background: var(--bg-primary);
}

.dark .background-animation {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

.dark .shape {
  background: rgba(28, 70, 110, 0.1);
}

.dark .login-card {
  background: rgba(30, 41, 59, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-primary);
}

.dark .login-header h1 {
  color: var(--text-primary);
}

.dark .login-header p {
  color: var(--text-secondary);
}

.dark .form-group label {
  color: var(--text-secondary);
}

.dark .form-group.focused label {
  color: var(--theme-primary);
}

.dark .form-group input {
  background: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .form-group input:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 4px var(--theme-primary-lighter);
}

.dark .form-group.focused .input-icon {
  fill: var(--theme-primary);
}

.dark .input-icon {
  fill: var(--text-tertiary);
}

.dark .password-toggle {
  color: var(--text-secondary);
}

.dark .password-toggle:hover {
  background: var(--bg-tertiary);
}

.dark .remember-me {
  color: var(--text-secondary);
}

.dark .checkmark {
  background-color: var(--bg-primary);
  border-color: var(--border-primary);
}

.dark .remember-me input:checked ~ .checkmark {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
}

.dark .forgot-password {
  color: var(--theme-primary);
}

.dark .forgot-password:hover {
  color: var(--theme-primary-hover);
}

.dark .login-button {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-hover) 100%);
}

.dark .error-message {
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
  color: #fecaca;
  border-color: #dc2626;
}

.dark .divider::before {
  background: var(--border-primary);
}

.dark .divider span {
  background: rgba(30, 41, 59, 0.95);
  color: var(--text-secondary);
}

.dark .social-button {
  background: var(--bg-primary);
  border-color: var(--border-primary);
  color: var(--text-primary);
}

.dark .social-button:hover {
  background: var(--bg-tertiary);
  border-color: var(--border-secondary);
}

.dark .demo-info {
  background: var(--bg-tertiary);
  border-color: var(--theme-primary);
}

.dark .demo-info h3,
.dark .demo-info p {
  color: var(--text-primary);
}

.dark .demo-credentials span {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.dark .demo-button {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-hover) 100%);
}

/* ===== SCROLLBAR DARK MODE ===== */
.dark ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.dark ::-webkit-scrollbar-track {
  background: var(--bg-primary);
}

.dark ::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* ===== ANIMATIONS ===== */
.dark * {
  transition: background-color 0.3s ease, 
              border-color 0.3s ease, 
              color 0.3s ease;
}