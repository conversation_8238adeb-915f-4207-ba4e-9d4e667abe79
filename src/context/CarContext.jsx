import { createContext, useContext, useState, useEffect } from 'react'
import { carService } from '../services/carService'

const CarContext = createContext()

export const useCarContext = () => {
  const context = useContext(CarContext)
  if (!context) {
    throw new Error('useCarContext must be used within a CarProvider')
  }
  return context
}

export const CarProvider = ({ children }) => {
  const [cars, setCars] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [brands, setBrands] = useState([])
  const [locations, setLocations] = useState([])
  const [carTypes, setCarTypes] = useState([])
  const [filters, setFilters] = useState({
    search: '',
    brand: '',
    minPrice: '',
    maxPrice: '',
    year: '',
    condition: '',
    carType: '',
    location: ''
  })

  useEffect(() => {
    fetchCars()
    // Don't fetch filter options on every filter change
    if (brands.length === 0) {
      fetchFilterOptions()
    }
  }, [filters])

  const fetchCars = async () => {
    try {
      setLoading(true)
      setError(null)
      console.log('Fetching cars with filters:', filters)
      
      const data = await carService.getCars(filters)
      console.log('Received cars data:', data)
      
      setCars(data || [])
    } catch (error) {
      setError(error.message)
      console.error('Error in fetchCars:', error)
      setCars([]) // Set empty array on error
    } finally {
      setLoading(false)
    }
  }

  const fetchFilterOptions = async () => {
    try {
      console.log('Fetching filter options...')
      
      const [brandsData, locationsData, carTypesData] = await Promise.all([
        carService.getBrands(),
        carService.getLocations(),
        carService.getCarTypes()
      ])
      
      console.log('Filter options:', { brandsData, locationsData, carTypesData })
      
      setBrands(brandsData || [])
      setLocations(locationsData || [])
      setCarTypes(carTypesData || [])
    } catch (error) {
      console.error('Error fetching filter options:', error)
    }
  }

  const addCar = async (carData) => {
    try {
      const newCar = await carService.createCar(carData)
      setCars(prev => [newCar, ...prev])
      return { success: true, car: newCar }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const updateCar = async (id, carData) => {
    try {
      const updatedCar = await carService.updateCar(id, carData)
      setCars(prev => prev.map(car => car.id === id ? updatedCar : car))
      return { success: true, car: updatedCar }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const deleteCar = async (id) => {
    try {
      await carService.deleteCar(id)
      setCars(prev => prev.filter(car => car.id !== id))
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  const value = {
    cars,
    loading,
    error,
    filters,
    setFilters,
    brands,
    locations,
    carTypes,
    addCar,
    updateCar,
    deleteCar,
    refetch: fetchCars
  }

  return <CarContext.Provider value={value}>{children}</CarContext.Provider>
}