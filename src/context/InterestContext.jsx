// contexts/InterestContext.js
import React, { createContext, useContext, useState, useEffect } from 'react'

const InterestContext = createContext()

export const useInterest = () => {
  const context = useContext(InterestContext)
  if (!context) {
    throw new Error('useInterest must be used within InterestProvider')
  }
  return context
}

export const InterestProvider = ({ children }) => {
  const [interestedCars, setInterestedCars] = useState([])
  const [loading, setLoading] = useState(true)

  // โหลดข้อมูลจาก localStorage เมื่อเริ่มต้น
  useEffect(() => {
    try {
      const savedInterests = localStorage.getItem('interestedCars')
      if (savedInterests) {
        const parsed = JSON.parse(savedInterests)
        console.log('Loaded interests from localStorage:', parsed) // Debug
        setInterestedCars(parsed)
      }
    } catch (error) {
      console.error('Error loading interested cars:', error)
      setInterestedCars([])
    } finally {
      setLoading(false)
    }
  }, [])

  // บันทึกลง localStorage เมื่อข้อมูลเปลี่ยน
  useEffect(() => {
    if (!loading) {
      console.log('Saving interests to localStorage:', interestedCars) // Debug
      localStorage.setItem('interestedCars', JSON.stringify(interestedCars))
    }
  }, [interestedCars, loading])

  const addToInterest = (car) => {
    console.log('Adding car to interest:', car) // Debug
    setInterestedCars(prev => {
      const exists = prev.some(interestedCar => interestedCar.id === car.id)
      if (!exists) {
        const newCar = {
          ...car,
          addedToInterestAt: new Date().toISOString()
        }
        const newInterests = [...prev, newCar]
        console.log('New interests array:', newInterests) // Debug
        return newInterests
      }
      return prev
    })
  }

  const removeFromInterest = (carId) => {
    console.log('Removing car from interest:', carId) // Debug
    setInterestedCars(prev => {
      const newInterests = prev.filter(car => car.id !== carId)
      console.log('After removal:', newInterests) // Debug
      return newInterests
    })
  }

  const toggleInterest = (car) => {
    const isInterested = interestedCars.some(interestedCar => interestedCar.id === car.id)
    
    if (isInterested) {
      removeFromInterest(car.id)
      return false
    } else {
      addToInterest(car)
      return true
    }
  }

  const isCarInterested = (carId) => {
    return interestedCars.some(car => car.id === carId)
  }

  const getInterestedCarsCount = () => {
    return interestedCars.length
  }

  const clearAllInterests = () => {
    setInterestedCars([])
  }

  const value = {
    interestedCars,
    addToInterest,
    removeFromInterest,
    toggleInterest,
    isCarInterested,
    getInterestedCarsCount,
    clearAllInterests,
    loading
  }

  return (
    <InterestContext.Provider value={value}>
      {children}
    </InterestContext.Provider>
  )
}